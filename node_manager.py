#!/usr/bin/env python3
import asyncio
import aiohttp
import time
import json
import os
import logging
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Default node if all tests fail
DEFAULT_NODE = "sass-node3.chatshare.biz"

class NodeManager:
    """
    Manages Chatshare nodes, performs speedtests, and selects the fastest node.
    """
    def __init__(self, config_path: str = "/app/node_config.json"):
        self.config_path = config_path
        self.lock = asyncio.Lock()
        self.current_node = DEFAULT_NODE
        self.last_test_time = None
        self.test_results = {}
        self.background_task = None
        self.is_running = False
        
        # Load config if exists
        self._load_config()
    
    def _load_config(self) -> None:
        """Load node configuration from file if it exists."""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r') as f:
                    config = json.load(f)
                    self.current_node = config.get('current_node', DEFAULT_NODE)
                    self.last_test_time = config.get('last_test_time')
                    self.test_results = config.get('test_results', {})
                    logger.info(f"Loaded node config: current_node={self.current_node}")
            else:
                logger.info(f"No node config found at {self.config_path}, using defaults")
        except Exception as e:
            logger.error(f"Error loading node config: {e}")
    
    def _save_config(self) -> None:
        """Save node configuration to file."""
        try:
            config = {
                'current_node': self.current_node,
                'last_test_time': self.last_test_time,
                'test_results': self.test_results
            }
            
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            
            with open(self.config_path, 'w') as f:
                json.dump(config, f)
            logger.info(f"Saved node config to {self.config_path}")
        except Exception as e:
            logger.error(f"Error saving node config: {e}")
    
    def get_node_urls(self) -> List[str]:
        """Generate URLs for all 64 Chatshare nodes."""
        urls = []
        
        # Generate node1-32
        for i in range(1, 33):
            urls.append(f"node{i}.chatshare.biz")
        
        # Generate sass-node1-32
        for i in range(1, 33):
            urls.append(f"sass-node{i}.chatshare.biz")
        
        return urls
    
    async def test_node(self, node_url: str) -> Tuple[str, Optional[float]]:
        """
        Test response time for a specific node.
        Returns (node_url, response_time_in_ms) or (node_url, None) if failed.
        """
        url = f"https://{node_url}/backend-api/models"
        
        try:
            start_time = time.time()
            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=5) as response:
                    if response.status == 200:
                        end_time = time.time()
                        response_time = (end_time - start_time) * 1000  # Convert to ms
                        return node_url, response_time
                    else:
                        logger.warning(f"Node {node_url} returned status {response.status}")
                        return node_url, None
        except asyncio.TimeoutError:
            logger.warning(f"Timeout testing node {node_url}")
            return node_url, None
        except Exception as e:
            logger.warning(f"Error testing node {node_url}: {e}")
            return node_url, None
    
    async def run_speedtest(self) -> Dict[str, Any]:
        """
        Run speedtest on all nodes and select the fastest one.
        Returns a dictionary with test results.
        """
        async with self.lock:
            logger.info("Starting node speedtest")
            node_urls = self.get_node_urls()
            
            # Run tests concurrently
            tasks = [self.test_node(node_url) for node_url in node_urls]
            results = await asyncio.gather(*tasks)
            
            # Process results
            successful_tests = {}
            failed_count = 0
            
            for node_url, response_time in results:
                if response_time is not None:
                    successful_tests[node_url] = response_time
                else:
                    failed_count += 1
            
            # Update test results
            self.test_results = {
                'timestamp': datetime.now().isoformat(),
                'successful_tests': successful_tests,
                'failed_count': failed_count,
                'total_tested': len(node_urls)
            }
            
            # Select fastest node
            if successful_tests:
                fastest_node = min(successful_tests.items(), key=lambda x: x[1])[0]
                self.current_node = fastest_node
                logger.info(f"Selected fastest node: {fastest_node} ({successful_tests[fastest_node]:.2f}ms)")
            else:
                # Fallback to default if all tests failed
                self.current_node = DEFAULT_NODE
                logger.warning(f"All node tests failed, falling back to default node: {DEFAULT_NODE}")
            
            # Update last test time
            self.last_test_time = datetime.now().isoformat()
            
            # Save config
            self._save_config()
            
            return {
                'current_node': self.current_node,
                'last_test_time': self.last_test_time,
                'successful_tests': len(successful_tests),
                'failed_tests': failed_count,
                'total_tested': len(node_urls),
                'fastest_response_time': successful_tests.get(self.current_node)
            }
    
    async def start_background_task(self) -> None:
        """Start background task for periodic node selection."""
        if self.is_running:
            logger.warning("Background task already running")
            return
        
        self.is_running = True
        logger.info("Starting background node selection task")
        
        while self.is_running:
            try:
                await self.run_speedtest()
                # Wait for 10 minutes
                await asyncio.sleep(600)
            except Exception as e:
                logger.error(f"Error in background node selection task: {e}")
                # Wait a bit before retrying
                await asyncio.sleep(60)
    
    def stop_background_task(self) -> None:
        """Stop background task."""
        logger.info("Stopping background node selection task")
        self.is_running = False
    
    def get_current_node(self) -> Dict[str, Any]:
        """Get information about the current node."""
        return {
            'current_node': self.current_node,
            'last_test_time': self.last_test_time,
            'test_results': self.test_results
        }
    
    def get_api_base_url(self) -> str:
        """Get the current API base URL."""
        return f"https://{self.current_node}"

# Create a global instance
node_manager = NodeManager()
