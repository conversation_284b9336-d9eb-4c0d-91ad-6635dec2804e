services:
  # --- AI Gateway & Proxy Service ---
  ai-g4fcs:
    image: ghcr.io/thinkfar/ai/g4fcs:main
    container_name: ai-g4fcs
    restart: unless-stopped # Ensures the service restarts unless manually stopped
    shm_size: "3g" # Shared memory size, can be important for some AI model operations
    volumes:
      - ./har_and_cookies:/app/har_and_cookies # Mounts host directory for HAR files and cookies, used for account interaction
      - ./generated_media:/app/generated_media # Mounts host directory for any media generated by underlying models
      - ./authentication_files:/app/authentication_files # Mounts host directory for authentication credentials
    environment:
      - PROXY_DOMAIN=ai-px-cs.snacne.com # Domain for the proxy service
      # - G4F_LOGIN_URL=... # Optional: URL for automated login if needed
      # Account rotation configuration for managing multiple AI service accounts
      - REMOTE_ACCOUNTS_URL=https://gist.github.com/ThinkFar/4b61378f3e46ca8d5a86e5da669ffe51/raw/auth_g4fcs.json # URL to fetch account credentials
      - RATE_LIMIT_ROTATION_ENABLED=false  # Enables automatic account rotation upon hitting rate limits
      - ROTATION_STRATEGY=round-robin # Strategy for selecting the next account
      - REMOTE_CHECK_INTERVAL=10 # Interval in seconds to check for remote account updates
      # Memory management configuration for the service
      - MAX_REQUEST_TIMES=1000
      - MAX_RATE_LIMITED_ACCOUNTS=1000
      - MAX_PENDING_CHANGES=1000
    ports:
      - "8082:8082" # Exposes the URL masking proxy port
      - "8084:8084" # Exposes the account metrics API port

  # --- Primary API Service (e.g., for Open WebUI) ---
  # Commented out new-api service
#  new-api:
#    image: calciumion/new-api:latest
#    container_name: new-api
#    restart: unless-stopped # Ensures the service restarts unless manually stopped
#    command: --log-dir /app/logs # Specifies the command to run and a custom log directory
#    ports:
#      - "3000:3000" # Exposes the API service port (e.g., for Open WebUI to connect to)
#    volumes:
#      - ./data:/data # Mounts host directory for persistent API data
#      - ./logs:/app/logs # Mounts host directory for API logs
#    environment:
#      - SQL_DSN=${DB_USER}:${DB_PASSWORD}@tcp(mysql:3306)/new-api # Data Source Name for MySQL connection
#      - REDIS_CONN_STRING=redis://redis # Connection string for Redis
#      - TZ=UTC # Sets the timezone to UTC
#    depends_on:
#      redis: # Ensures Redis starts before new-api
#        condition: service_started
#      mysql: # Ensures MySQL is healthy before new-api starts
#        condition: service_healthy
#      ai-g4fcs: # Ensures ai-g4fcs (proxy) starts before new-api, if it acts as an upstream
#        condition: service_started
#    healthcheck: # Defines how to check if the service is healthy
#      test: >
#        wget -q -O - http://localhost:3000/api/status | grep -q '"success":\\s*true' || exit 1
#      interval: 30s # How often to run the health check
#      timeout: 10s  # How long to wait for a response
#      retries: 3    # How many times to retry if it fails

  # --- Web UI for LLMs (like ChatGPT) ---
  open-webui:
    image: ghcr.io/open-webui/open-webui:main
    container_name: open-webui
    restart: unless-stopped # Ensures the service restarts unless manually stopped
    ports:
      - "8080:8080" # Exposes the Open WebUI interface (changed from 3000 to avoid conflict with new-api)
    volumes:
      - open-webui_data:/app/backend/data # Uses a named volume for persistent Open WebUI data
#    depends_on:
#      - new-api # Ensures new-api (which it uses as a backend) is started
    # environment: # Example environment variable for connecting to a backend LLM API
    #   - OPENAI_API_BASE_URL=http://new-api:3000/v1

  # --- Alternative Chat Interface ---
  # Commented out ChatNio service
  # chatnio:
  #   image: programzmh/chatnio:latest
  #   container_name: chatnio
  #   restart: unless-stopped # Ensures the service restarts unless manually stopped
  #   ports:
  #     - "8085:8094" # Exposes the ChatNio web interface
  #   ulimits: # Sets resource limits for the container
  #     nofile:
  #       soft: 65535
  #       hard: 65535
  #   volumes:
  #     - ./chatnio/config:/config # Mounts host directory for ChatNio configuration
  #     - ./chatnio/logs:/logs     # Mounts host directory for ChatNio logs
  #     - ./chatnio/storage:/storage # Mounts host directory for ChatNio storage
  #   environment:
  #     - MYSQL_HOST=mysql # Hostname for the MySQL database
  #     - MYSQL_PORT=3306 # Port for the MySQL database
  #     - MYSQL_DB=chatnio # Specifies the database ChatNio needs
  #     - MYSQL_USER=root # MySQL user (consider a less privileged user for production)
  #     - MYSQL_PASSWORD=${MYSQL_ROOT_PASSWORD_VAR} # MySQL root password (from shell variable)
  #     - REDIS_HOST=redis # Hostname for Redis
  #     - REDIS_PORT=6379 # Port for Redis
  #     - SERVE_STATIC=true # Instructs ChatNio to serve static assets
  #   depends_on:
  #     mysql: # Ensures MySQL is healthy before ChatNio starts
  #       condition: service_healthy
  #     redis: # Ensures Redis starts before ChatNio
  #       condition: service_started
  #   links: # Legacy way to link containers, depends_on is generally preferred
  #     - mysql
  #     - redis

  # --- In-Memory Data Store (Cache, Message Broker) ---
  redis:
    image: redis:latest
    container_name: redis
    restart: unless-stopped # Ensures the service restarts unless manually stopped
    # No ports exposed externally, accessed by other services on the internal Docker network

  # --- Relational Database Service ---
  # Commented out MySQL service as it's no longer needed after removing new-api
#  mysql:
#    image: mysql:8.2
#    container_name: mysql
#    restart: unless-stopped # Ensures the service restarts unless manually stopped
#    environment:
#      MYSQL_USER: ${DB_USER} # Creates a non-root user (from shell variable)
#      MYSQL_PASSWORD: ${DB_PASSWORD} # Password for the non-root user (from shell variable)
#      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD_VAR} # Root password for MySQL (from shell variable)
##      MYSQL_DATABASE: new-api # Creates an initial database for new-api
#    volumes:
#      - mysql_data:/var/lib/mysql # Uses a named volume for persistent MySQL data
#      # Commented out ChatNio-related initialization script
#      # - ./mysql-init.sql:/docker-entrypoint-initdb.d/mysql-init.sql # Runs this SQL script on initialization (e.g., to create chatnio DB)
#    healthcheck: # Defines how to check if MySQL is healthy
#      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-uroot", "-p${MYSQL_ROOT_PASSWORD_VAR}"]
#      interval: 10s
#      timeout: 5s
#      retries: 20
#      start_period: 30s # Gives MySQL time to start up before health checks begin
#    # No ports exposed externally, accessed by other services on the internal Docker network

  # --- Docker Container Auto-Updater ---
  watchtower:
    image: containrrr/watchtower
    container_name: watchtower
    restart: always # Ensures watchtower is always running to check for updates
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock # Allows watchtower to interact with the Docker daemon
    command: --interval 3600 --stop-timeout 60s # Checks for new images every hour (3600s) and gives containers 60s to stop

  # --- LiteLLM Proxy, DB, and Prometheus ---

  # --- LiteLLM: Universal LLM API Gateway ---
  litellm:
    image: ghcr.io/berriai/litellm:main-latest # Specifies the LiteLLM image to use
    container_name: litellm # Added for consistency
    restart: unless-stopped # Added for consistency
    volumes:
     - ./litellm_config.yaml:/app/config.yaml # Mounts custom LiteLLM configuration file
    command:
     - "--config=/app/config.yaml"    # Tells LiteLLM to use the custom config
    ports:
      - "4000:4000" # Exposes LiteLLM API endpoint and UI
    environment:
      # Connects LiteLLM to its PostgreSQL database
      DATABASE_URL: "postgresql://${LITELLM_DB_USER}:${LITELLM_DB_PASSWORD}@postgres:5432/${LITELLM_DB_NAME}"
      LITELLM_MASTER_KEY: "${LITELLM_MASTER_KEY_VAR}" # Master key for LiteLLM Admin UI and API key generation.
    # If you have API keys, create a .env file and uncomment the following lines
    # env_file:
    #   - ./.env # Load environment variables from a .env file (for API keys)
    depends_on:
      postgres: # Ensures the PostgreSQL database starts before LiteLLM
        condition: service_started
      redis: # Ensures Redis starts before LiteLLM
        condition: service_started
      ai-g4fcs: # Ensures the GPT4Free container starts before LiteLLM
        condition: service_started
    healthcheck: # Defines how to check if LiteLLM is healthy
      test: [
          "CMD",
          "curl",
          "-f",
          "http://localhost:4000/health/liveliness || exit 1", # Checks the liveliness endpoint
        ]
      interval: 30s
      timeout: 10s
      retries: 6
      start_period: 40s # Gives LiteLLM time to start before health checks begin

  # --- PostgreSQL Database for LiteLLM ---
  postgres: # This is the PostgreSQL database service for LiteLLM
    image: postgres:16
    restart: always # Ensures the database is always running
    container_name: litellm_db # Specific container name for the LiteLLM database
    environment:
      POSTGRES_DB: ${LITELLM_DB_NAME} # Sets the database name (from shell variable)
      POSTGRES_USER: ${LITELLM_DB_USER} # Sets the database user (from shell variable)
      POSTGRES_PASSWORD: ${LITELLM_DB_PASSWORD} # Sets the database password (from shell variable)
    # ports:
    #   - "5432:5432" # Exposes PostgreSQL port (standard port) - primarily for external debugging/management if needed
    volumes:
      - postgres_data:/var/lib/postgresql/data # Uses a named volume for persistent PostgreSQL data
    healthcheck: # Defines how to check if PostgreSQL is ready
      test: ["CMD-SHELL", "pg_isready -d ${LITELLM_DB_NAME} -U ${LITELLM_DB_USER}"] # Checks if the DB is accepting connections
      interval: 1s
      timeout: 5s
      retries: 10

  # --- Prometheus: Monitoring System --- (removed from setup)
#  prometheus:
#    image: prom/prometheus
#    container_name: prometheus # Specific container name for Prometheus
#    volumes:
#      - prometheus_data:/prometheus # Uses a named volume for persistent Prometheus data (metrics storage)
#      - ./prometheus.yml:/etc/prometheus/prometheus.yml # Mounts the Prometheus configuration file from the host
#    ports:
#      - "8099:9090" # Exposes the Prometheus web UI and API
#    command:
#      - "--config.file=/etc/prometheus/prometheus.yml" # Tells Prometheus to use the mounted config file
#      - "--storage.tsdb.path=/prometheus" # Specifies the path for time series database storage
#      - "--storage.tsdb.retention.time=15d" # Sets metrics retention to 15 days
#    restart: always # Ensures Prometheus is always running

volumes:
  # Named volume for MySQL data persistence (commented out as MySQL is no longer used)
  # mysql_data:
  # Named volume for Open WebUI data persistence
  open-webui_data:
  # Named volume for Prometheus data persistence (not used)
#  prometheus_data:
#    driver: local # Specifies the local driver for this volume
  # Named volume for PostgreSQL data persistence (for LiteLLM)
  postgres_data:
    name: litellm_postgres_data # Explicitly names the volume on the host