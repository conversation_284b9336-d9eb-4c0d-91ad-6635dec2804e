#!/usr/bin/env python3
"""
Service script for the account rotation system.
This script is used to start the account rotator as a background service.
"""
import os
import sys
import time
import asyncio
import logging
import signal
import argparse
from typing import Dict, Any, Optional

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
# Also add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
# Also add /app to the path (for Docker)
sys.path.insert(0, "/app")

# Check if aiohttp is installed
try:
    import aiohttp
    print("aiohttp is already installed")
except ImportError:
    print("aiohttp is not installed.")
    print("Please install it using: apt-get install python3-aiohttp")
    print("Continuing anyway, but this will cause problems with remote account fetching.")

# Import the account rotator
try:
    from account_rotator import account_rotator
    print("Successfully imported account_rotator module")
except ImportError as e:
    print(f"Error importing account_rotator module: {e}")
    print("Trying alternative import paths...")

    try:
        # Try importing from absolute path
        sys.path.insert(0, "/app/account_rotator")
        from account_rotator import account_rotator
        print("Successfully imported account_rotator module from /app/account_rotator")
    except ImportError as e2:
        print(f"Error importing account_rotator module from /app/account_rotator: {e2}")

        try:
            # Try importing the module directly
            import account_rotator
            print("Successfully imported account_rotator module directly")
        except ImportError as e3:
            print(f"Error importing account_rotator module directly: {e3}")

            # Try one more approach - import the individual modules directly
            try:
                print("Trying to import individual modules directly...")
                sys.path.insert(0, "/app")
                from account_rotator.config import Config
                from account_rotator.file_manager import FileManager
                from account_rotator.account_manager import AccountManager
                from account_rotator.background_tasks import BackgroundTasks

                # Create a mock account_rotator module
                class AccountRotator:
                    def __init__(self):
                        self.config = Config()
                        self.file_manager = FileManager(self.config)
                        self.account_manager = AccountManager(self.config, self.file_manager)
                        self.background_tasks = BackgroundTasks(self.config, self.file_manager, self.account_manager)

                    async def start(self):
                        await self.background_tasks.start()

                    async def stop(self):
                        await self.background_tasks.stop()

                account_rotator = AccountRotator()
                print("Successfully created mock account_rotator module")
            except ImportError as e4:
                print(f"Error importing individual modules: {e4}")
                print("All import attempts failed. Exiting.")
                sys.exit(1)

# Configure logging (will be properly initialized later)
logger = logging.getLogger(__name__)

async def main():
    """Main entry point for the account rotator service."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Account Rotator Service')
    parser.add_argument('--daemon', action='store_true', help='Run as daemon')
    args = parser.parse_args()

    # Setup signal handlers
    # Get the current event loop
    loop = asyncio.get_running_loop()

    # Define shutdown coroutine
    async def shutdown():
        logger.info("Received shutdown signal")
        await account_rotator.stop()
        # No need to stop the loop as asyncio.run will handle that

    # Register signal handlers
    for sig in (signal.SIGINT, signal.SIGTERM):
        loop.add_signal_handler(sig, lambda: asyncio.create_task(shutdown()))

    try:
        # Start account rotator
        await account_rotator.start()

        # Keep running until stopped
        while True:
            await asyncio.sleep(1)
    except asyncio.CancelledError:
        pass
    finally:
        await account_rotator.stop()

if __name__ == "__main__":
    print("Starting account_rotator_service.py...")

    # Use authentication_files directory for logs
    logs_dir = '/app/authentication_files'
    try:
        # Check if the logs directory exists and is writable
        if os.path.exists(logs_dir):
            if os.access(logs_dir, os.W_OK):
                print(f"Logs directory {logs_dir} exists and is writable")
            else:
                print(f"Logs directory {logs_dir} exists but is not writable")
                # Try to use a different directory
                logs_dir = './authentication_files'
        else:
            # Try to create the logs directory
            try:
                os.makedirs(logs_dir, exist_ok=True)
                print(f"Created logs directory {logs_dir}")
            except Exception as e:
                print(f"Error creating logs directory {logs_dir}: {e}")
                # Try to use a different directory
                logs_dir = './authentication_files'

        # If we're using a different directory, make sure it exists
        if logs_dir == './authentication_files':
            try:
                os.makedirs(logs_dir, exist_ok=True)
                print(f"Created or verified fallback logs directory {logs_dir}")
            except Exception as e2:
                print(f"Error creating fallback logs directory {logs_dir}: {e2}")
                # Continue anyway

        # Print current user and group
        import pwd, grp
        uid = os.getuid()
        gid = os.getgid()
        user = pwd.getpwuid(uid).pw_name
        group = grp.getgrgid(gid).gr_name
        print(f"Running as user {user} (uid={uid}) group {group} (gid={gid})")

        # Print permissions of the logs directory
        if os.path.exists(logs_dir):
            import stat
            st = os.stat(logs_dir)
            print(f"Logs directory {logs_dir} permissions: {stat.filemode(st.st_mode)}")
            print(f"Logs directory {logs_dir} owner: {pwd.getpwuid(st.st_uid).pw_name} ({st.st_uid})")
            print(f"Logs directory {logs_dir} group: {grp.getgrgid(st.st_gid).gr_name} ({st.st_gid})")
    except Exception as e:
        print(f"Error checking logs directory: {e}")
        # Continue anyway

    # Configure logging with the determined logs directory
    log_file = os.path.join(logs_dir, 'account_rotator.log')
    print(f"Configuring logging to file: {log_file}")
    try:
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler(log_file)
            ]
        )
        print(f"Logging configured successfully to {log_file}")
    except Exception as e:
        print(f"Error configuring logging: {e}")
        # Configure logging with just the console handler
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler()
            ]
        )
        print("Logging configured with console handler only")

    # Run main function
    print("Initializing event loop...")
    try:
        print("Starting main function...")
        asyncio.run(main())
        print("Main function completed.")
    except KeyboardInterrupt:
        print("Received KeyboardInterrupt, shutting down...")
    except Exception as e:
        print(f"Error in main function: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
