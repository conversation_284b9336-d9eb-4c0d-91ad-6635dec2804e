#!/usr/bin/env python3
import sys
import os
import re
from pathlib import Path
import textwrap

# --- Define absolute paths within the container ---
APP_DIR = Path('/app')
G4F_DIR = APP_DIR / 'g4f'
PROVIDER_DIR = G4F_DIR / 'Provider'
NEEDS_AUTH_DIR = PROVIDER_DIR / 'needs_auth'

needs_auth_init_path = NEEDS_AUTH_DIR / '__init__.py'
models_py_path = G4F_DIR / 'models.py' # Path to the main models file
provider_init_path = PROVIDER_DIR / '__init__.py'

# --- Helper Functions ---
# (Keep the existing add_import_lines and append_lines_if_missing functions)
def add_import_lines(filepath: Path, lines_to_add: list[str], check_existing: bool = True):
    try:
        content = filepath.read_text() if filepath.exists() else ""
        content_lines = content.splitlines()
        if check_existing:
            content_stripped_lines = {line.strip() for line in content_lines}
            lines_already_exist = [line for line in lines_to_add if line.strip() in content_stripped_lines]
            if len(lines_already_exist) == len(lines_to_add): print(f"    Skipping add: All lines already exist in {filepath.name}"); return True
            elif lines_already_exist: print(f"    Warning: Some lines already exist in {filepath.name}: {lines_already_exist}"); lines_to_add = [line for line in lines_to_add if line.strip() not in content_stripped_lines];
            if not lines_to_add: return True # All requested lines already exist

        last_import_index = -1
        for i, line in enumerate(content_lines):
            stripped_line = line.strip()
            if stripped_line.startswith("from ") or stripped_line.startswith("import "): last_import_index = i

        insert_index = last_import_index + 1 if last_import_index != -1 else 0
        if last_import_index != -1: print(f"    Determined last import line for {filepath.name} at index {last_import_index}, inserting after.")
        else: print(f"    WARNING: No import lines found in {filepath.name}. Inserting at the beginning.")

        new_content_lines = content_lines[:insert_index]
        new_content_lines.extend(lines_to_add)
        # Add a blank line after imports if inserting before existing code (and not at the end)
        if insert_index < len(content_lines) and content_lines[insert_index].strip() and not content_lines[insert_index].strip().startswith('#'):
            new_content_lines.append("")
        new_content_lines.extend(content_lines[insert_index:])

        filepath.write_text("\n".join(new_content_lines) + "\n")
        print(f"    Added {len(lines_to_add)} line(s) to {filepath.name} at index {insert_index}.")
        print(f"    Lines added: {lines_to_add}")
        return True
    except FileNotFoundError: print(f"    ERROR: File not found: {filepath}"); return False
    except Exception as e: print(f"    ERROR modifying {filepath.name}: {type(e).__name__}: {e}"); return False

def append_lines_if_missing(filepath: Path, lines_to_add_block: str, check_content: str = None):
    try:
        content = ""
        if filepath.exists(): content = filepath.read_text()
        content_marker = check_content or (lines_to_add_block.strip().splitlines()[0] if isinstance(lines_to_add_block, str) else lines_to_add_block[0].strip())

        if content_marker in content:
            print(f"    Skipping append: Content marker '{content_marker}' already exists in {filepath.name}")
            return True

        with filepath.open("a") as f:
            if content and not content.endswith('\n'): f.write("\n") # Ensure newline before appending
            if isinstance(lines_to_add_block, str):
                f.write(lines_to_add_block + "\n")
                print(f"    Appended block starting with '{content_marker}' to {filepath.name}")
            else:
                f.write("\n".join(lines_to_add_block) + "\n")
                print(f"    Appended {len(lines_to_add_block)} lines starting with '{content_marker}' to {filepath.name}")
        return True
    except FileNotFoundError: print(f"    ERROR: File not found: {filepath}"); return False
    except Exception as e: print(f"    ERROR appending to {filepath.name}: {type(e).__name__}: {e}"); return False

# --- Content Blocks to Add/Append ---
needs_auth_imports_to_add = [
    'from .Chatshare import Chatshare',
    'from .ChatshareAccount import ChatshareAccount',
    'from .ChatshareRotator import ChatshareRotator'
]

provider_init_import_to_add = 'from .needs_auth import Chatshare, ChatshareAccount, ChatshareRotator'
# Provider registration logic remains the same - it modifies the lists in Provider/__init__.py's scope
provider_init_registration_code = textwrap.dedent("""

    # --- Start Appended Registration Logic ---
    # This block registers the provider classes themselves
    try:
        # Dynamically check if classes exist before appending, preventing duplicates if script runs multiple times
        target_providers = locals().get('__providers__', globals().get('__providers__'))
        target_all = locals().get('__all__', globals().get('__all__'))
        target_map = locals().get('__map__', globals().get('__map__'))
        target_utils = locals().get('ProviderUtils', globals().get('ProviderUtils'))

        if target_providers is None: print("    WARNING: __providers__ list not found for registration.")
        elif target_all is None: print("    WARNING: __all__ list not found for registration.")
        elif target_map is None: print("    WARNING: __map__ dict not found for registration.")
        else:
            providers_changed = False
            # Try to import if not already in scope (might be redundant if import added above, but safe)
            if 'Chatshare' not in locals() and 'Chatshare' not in globals():
                 try: from .needs_auth import Chatshare; print("    INFO: Chatshare imported dynamically.")
                 except ImportError: print("    ERROR: Could not dynamically import Chatshare.")
            if 'ChatshareAccount' not in locals() and 'ChatshareAccount' not in globals():
                 try: from .needs_auth import ChatshareAccount; print("    INFO: ChatshareAccount imported dynamically.")
                 except ImportError: print("    ERROR: Could not dynamically import ChatshareAccount.")
            if 'ChatshareRotator' not in locals() and 'ChatshareRotator' not in globals():
                 try: from .needs_auth import ChatshareRotator; print("    INFO: ChatshareRotator imported dynamically.")
                 except ImportError: print("    ERROR: Could not dynamically import ChatshareRotator.")

            # Register Chatshare
            if isinstance(target_providers, list) and "Chatshare" not in [p.__name__ for p in target_providers]:
                if 'Chatshare' in locals() or 'Chatshare' in globals():
                    print("    Registering Chatshare...")
                    target_providers.append(Chatshare)
                    if isinstance(target_all, list) and "Chatshare" not in target_all: target_all.append("Chatshare")
                    if isinstance(target_map, dict) and "Chatshare" not in target_map: target_map["Chatshare"] = Chatshare
                    providers_changed = True
                    print("    Chatshare registered.")
                else: print("    ERROR: Chatshare class not available for registration.")

            # Register ChatshareAccount
            if isinstance(target_providers, list) and "ChatshareAccount" not in [p.__name__ for p in target_providers]:
                 if 'ChatshareAccount' in locals() or 'ChatshareAccount' in globals():
                    print("    Registering ChatshareAccount...")
                    target_providers.append(ChatshareAccount)
                    if isinstance(target_all, list) and "ChatshareAccount" not in target_all: target_all.append("ChatshareAccount")
                    if isinstance(target_map, dict) and "ChatshareAccount" not in target_map: target_map["ChatshareAccount"] = ChatshareAccount
                    providers_changed = True
                    print("    ChatshareAccount registered.")
                 else: print("    ERROR: ChatshareAccount class not available for registration.")

            # Register ChatshareRotator
            if isinstance(target_providers, list) and "ChatshareRotator" not in [p.__name__ for p in target_providers]:
                 if 'ChatshareRotator' in locals() or 'ChatshareRotator' in globals():
                    print("    Registering ChatshareRotator...")
                    target_providers.append(ChatshareRotator)
                    if isinstance(target_all, list) and "ChatshareRotator" not in target_all: target_all.append("ChatshareRotator")
                    if isinstance(target_map, dict) and "ChatshareRotator" not in target_map: target_map["ChatshareRotator"] = ChatshareRotator
                    providers_changed = True
                    print("    ChatshareRotator registered.")
                 else: print("    ERROR: ChatshareRotator class not available for registration.")

            # Update ProviderUtils.convert map if changes occurred
            if providers_changed:
                 if target_utils and hasattr(target_utils, 'convert') and isinstance(target_map, dict):
                     target_utils.convert = target_map # Update the mapping used by g4f.Provider.ProviderUtils
                     print("    ProviderUtils.convert mapping updated.")
                 else: print("    WARNING: ProviderUtils not found or lacks .convert attribute, could not update map.")

    except NameError as e: print(f"    WARNING: NameError during provider registration: {e}. Imports might be missing.")
    except Exception as e: print(f"    ERROR during provider registration: {type(e).__name__}: {e}")
    # --- End Appended Registration Logic ---
    """)

# --- NEW: Code block to APPEND to g4f/models.py ---
models_py_registration_code = textwrap.dedent("""

# --- Start Appended Chatshare Model Registration ---
# This code runs when g4f.models is imported by the server
try:
    # Import necessary components *within this scope*
    # Use absolute imports assuming g4f is in the python path
    from g4f.Provider.needs_auth import Chatshare
    from g4f.providers.retry_provider import IterListProvider
    from g4f.providers.base_provider import BaseProvider
    from g4f.models import Model, ModelUtils # Import the necessary classes/objects

    print("  Models.py: Attempting Chatshare model registration...")

    prefix = "cs-" # Define the prefix

    # --- Get models supported by Chatshare ---
    try:
        chatshare_all_models = Chatshare.get_models() # Assumes this is synchronous
        chatshare_aliases = Chatshare.model_aliases.keys()
        chatshare_supported_model_names = set(chatshare_all_models) | set(chatshare_aliases)
        print(f"  Models.py: Chatshare supports: {', '.join(sorted(list(chatshare_supported_model_names)))}")
    except Exception as e:
        print(f"    Models.py: ERROR fetching models from Chatshare: {type(e).__name__}: {e}")
        chatshare_supported_model_names = set(Chatshare.model_aliases.keys()) | {Chatshare.default_model}
        print(f"    Models.py: WARN: Falling back to known Chatshare models/aliases.")

    models_updated_count = 0
    models_created_count = 0
    prefixed_models_created_count = 0

    # --- Phase 1: Integrate with existing G4F models ---
    for model_name in chatshare_supported_model_names:
        if model_name in ModelUtils.convert:
            model_obj = ModelUtils.convert[model_name]
            current_best = model_obj.best_provider
            if isinstance(current_best, IterListProvider):
                if Chatshare not in current_best.providers:
                    current_best.providers.append(Chatshare); models_updated_count += 1
            elif isinstance(current_best, type) and issubclass(current_best, BaseProvider):
                if current_best != Chatshare:
                    model_obj.best_provider = IterListProvider([current_best, Chatshare], shuffle=True); models_updated_count += 1
            elif current_best is None:
                 model_obj.best_provider = Chatshare; models_updated_count += 1
        else:
            base_provider = "OpenAI" if "gpt" in model_name else "Anthropic" if "claude" in model_name else "Google" if "gemini" in model_name else "xAI" if "grok" in model_name else "Deepseek" if "deepseek" in model_name else "Chatshare"
            new_model = Model(name=model_name, base_provider=base_provider, best_provider=Chatshare)
            ModelUtils.convert[model_name] = new_model; models_created_count += 1

    # --- Phase 2: Create prefixed models ---
    chatshare_primary_models = set(Chatshare.get_models()) if Chatshare.models else {Chatshare.default_model}
    for primary_model_name in chatshare_primary_models:
        prefixed_name = f"{prefix}{primary_model_name}"
        if prefixed_name not in ModelUtils.convert:
            base_provider = "OpenAI" if "gpt" in primary_model_name else "Anthropic" if "claude" in primary_model_name else "Google" if "gemini" in primary_model_name else "xAI" if "grok" in primary_model_name else "Deepseek" if "deepseek" in primary_model_name else "Chatshare"
            new_prefixed_model = Model(name=prefixed_name, base_provider=base_provider, best_provider=Chatshare)
            ModelUtils.convert[prefixed_name] = new_prefixed_model; prefixed_models_created_count += 1
        else:
             existing_model = ModelUtils.convert[prefixed_name]
             if existing_model.best_provider != Chatshare: existing_model.best_provider = Chatshare # Ensure exclusivity

    print(f"  Models.py: Chatshare registration summary - Integrated/Updated: {models_updated_count}, New: {models_created_count}, Prefixed: {prefixed_models_created_count}")

except ImportError as e: print(f"    Models.py: ERROR importing modules for Chatshare registration: {e}")
except Exception as e: print(f"    Models.py: ERROR during Chatshare model registration: {type(e).__name__}: {e}")
# --- End Appended Chatshare Model Registration ---
""")


# --- Create a startup script for the ChatShare API ---
def create_chatshare_api_startup_script():
    # Create a script that will be run by the container's runtime mechanism
    # (e.g., supervisor, or simply executed if no ENTRYPOINT/CMD)
    # to start both the ChatShare API and the URL Masking Proxy.
    startup_script_path = APP_DIR / 'start_services.py' # Renamed for clarity

    startup_script_content = textwrap.dedent(f"""
    #!/usr/bin/env python3
    import os
    import sys
    import time
    import subprocess
    import threading
    import signal
    import logging

    # Configure basic logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')

    # --- Service Configuration ---
    CHATSHARE_API_HOST = "0.0.0.0"
    CHATSHARE_API_PORT = 8081
    URL_MASKING_PROXY_HOST = "0.0.0.0"
    URL_MASKING_PROXY_PORT = 8082
    URL_PROXY_HOST = "0.0.0.0"
    URL_PROXY_PORT = 8083
    # Use environment variable for debug, default to False
    DEBUG_MODE = os.environ.get('G4F_DEBUG', 'false').lower() == 'true'
    LOG_LEVEL = "debug" if DEBUG_MODE else "info"
    # Check for PROXY_DOMAIN for logging
    PROXY_DOMAIN = os.environ.get("PROXY_DOMAIN")
    if PROXY_DOMAIN:
        logging.info(f"PROXY_DOMAIN detected: {{PROXY_DOMAIN}}. URL Masking will be active in ChatShare API.")
    else:
        logging.info("PROXY_DOMAIN not set. URL Masking will be inactive.")

    processes = [] # Keep track of subprocesses

    def run_service(name, command):
        logging.info(f"Starting {{name}} with command: {{' '.join(command)}}")
        try:
            # Use Popen to run in the background without blocking
            process = subprocess.Popen(command, stdout=sys.stdout, stderr=sys.stderr)
            processes.append(process)
            logging.info(f"{{name}} started with PID: {{process.pid}}")
            return process
        except Exception as e:
            logging.error(f"Error starting {{name}}: {{type(e).__name__}} - {{e}}", exc_info=True)
            return None

    def signal_handler(signum, frame):
        logging.info("Received termination signal. Stopping services...")
        for p in processes:
            if p.poll() is None: # Check if process is still running
                try:
                    logging.info(f"Terminating process PID {{p.pid}}...")
                    p.terminate() # Send SIGTERM
                    p.wait(timeout=5) # Wait for graceful shutdown
                except subprocess.TimeoutExpired:
                    logging.warning(f"Process PID {{p.pid}} did not terminate gracefully. Sending SIGKILL...")
                    p.kill() # Force kill
                except Exception as e:
                     logging.error(f"Error terminating process PID {{p.pid}}: {{e}}")
        logging.info("All services stopped.")
        sys.exit(0)

    # Register signal handlers for graceful shutdown
    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGINT, signal_handler)

    # --- Start Default G4F API (Port 8080) ---
    # Define the host and port for the default API
    G4F_DEFAULT_API_HOST = "0.0.0.0"
    G4F_DEFAULT_API_PORT = 8080
    # Invoke via uvicorn, using --factory
    g4f_command = [
        "uvicorn",
        "g4f.api:create_app", # Assume a create_app factory function exists
        f"--host={{G4F_DEFAULT_API_HOST}}",
        f"--port={{G4F_DEFAULT_API_PORT}}",
        "--factory", # Use factory pattern
        # Add reload/debug? Match behavior if needed
        *([] if not DEBUG_MODE else ["--reload", f"--log-level={{LOG_LEVEL}}"])
    ]
    run_service("Default G4F API", g4f_command)

    # --- Start ChatShare API (Port 8081) ---
    chatshare_command = [
        "uvicorn",
        "chatshare_api:create_app",
        f"--host={{CHATSHARE_API_HOST}}",
        f"--port={{CHATSHARE_API_PORT}}",
        "--factory",
        f"--log-level={{LOG_LEVEL}}",
        # Add reload if debug mode is enabled
        *(["--reload"] if DEBUG_MODE else [])
    ]
    run_service("ChatShare API", chatshare_command)

    # --- Start URL Proxy Service (Port 8083) ---
    url_proxy_command = [
        "uvicorn",
        "url_proxy:create_app",
        f"--host={{URL_PROXY_HOST}}",
        f"--port={{URL_PROXY_PORT}}",
        "--factory",
        f"--log-level={{LOG_LEVEL}}",
        # Add reload if debug mode is enabled
        *(["--reload"] if DEBUG_MODE else [])
    ]
    run_service("URL Proxy Service", url_proxy_command)

    # --- Start URL Masking Proxy (Port 8082) ---
    # Command needs to be a list for subprocess.Popen
    proxy_command_list = ["caddy", "run", "--config", "/app/Caddyfile", "--adapter", "caddyfile"]
    proxy_log_level = 'info' # Caddy uses its own logging config via Caddyfile
    run_service("URL Masking Proxy", proxy_command_list)

    # Keep the main script alive to monitor processes
    logging.info("Startup script running. Monitoring services...")
    try:
        while True:
            # Check if any process has exited unexpectedly
            for i, p in enumerate(processes):
                if p.poll() is not None:
                    logging.error(f"Process PID {{p.pid}} exited unexpectedly with code {{p.returncode}}. Exiting monitor.")
                    # Trigger shutdown for remaining processes
                    signal_handler(signal.SIGTERM, None)
                    sys.exit(1) # Exit with error code
            time.sleep(5) # Check every 5 seconds
    except KeyboardInterrupt:
        logging.info("KeyboardInterrupt received. Initiating shutdown...")
        signal_handler(signal.SIGINT, None)
    except Exception as e:
        logging.error(f"Main monitoring loop encountered an error: {{e}}", exc_info=True)
        signal_handler(signal.SIGTERM, None) # Attempt graceful shutdown
        sys.exit(1)
    """)

    try:
        startup_script_path.parent.mkdir(parents=True, exist_ok=True) # Ensure /app exists
        with open(startup_script_path, 'w') as f:
            f.write(startup_script_content)
        os.chmod(startup_script_path, 0o755)  # Make the script executable
        print(f"  Created and configured startup script: {startup_script_path}")
        return True
    except Exception as e:
        print(f"    ERROR creating startup script {startup_script_path}: {type(e).__name__}: {e}")
        return False

# --- Main Execution Logic ---
def patch_g4f_main():
    print("\n--- Applying ChatShare Patches to g4f ---")
    all_success = True

    # Step 1: Ensure needs_auth directory and __init__.py exist
    print("1. Ensuring needs_auth directory and __init__.py exist...")
    try:
        NEEDS_AUTH_DIR.mkdir(parents=True, exist_ok=True)
        if not needs_auth_init_path.exists():
            needs_auth_init_path.touch()
            print(f"    Created: {needs_auth_init_path}")
        else:
            print(f"    Exists: {needs_auth_init_path}")
    except Exception as e:
        print(f"    ERROR creating needs_auth structure: {e}"); all_success = False

    # Step 2: Add imports to needs_auth/__init__.py
    if all_success:
        print(f"\n2. Adding imports to {needs_auth_init_path.name}...")
        success = add_import_lines(needs_auth_init_path, needs_auth_imports_to_add)
        if not success: all_success = False

    # Step 3: Add import to Provider/__init__.py
    if all_success:
        print(f"\n3. Adding import to {provider_init_path.name}...")
        success = add_import_lines(provider_init_path, [provider_init_import_to_add])
        if not success: all_success = False

    # Step 4: Append registration logic to Provider/__init__.py
    if all_success:
        print(f"\n4. Appending registration logic to {provider_init_path.name}...")
        success = append_lines_if_missing(provider_init_path, provider_init_registration_code, check_content="# --- Start Appended Registration Logic ---")
        if not success: all_success = False

    # Step 5: Append model registration logic to models.py
    if all_success:
        print(f"\n5. Appending model registration to {models_py_path.name}...")
        success = append_lines_if_missing(models_py_path, models_py_registration_code, check_content="# --- Start Appended Chatshare Model Registration ---")
        if not success: all_success = False

    # Step 6: Create the combined startup script
    if all_success:
        print("\n6. Creating the combined service startup script...")
        success = create_chatshare_api_startup_script() # Call the updated function
        if not success: all_success = False

    print("\n--- Patching Complete --- ")
    if all_success:
        print("All steps completed successfully.")
    else:
        print("One or more steps failed. Please check the logs above.")
        sys.exit(1) # Exit with error if any step failed

if __name__ == "__main__":
    # Ensure the script is running from the intended directory context if needed
    # print(f"Running patch script from: {os.getcwd()}")
    # print(f"Target APP_DIR: {APP_DIR}")
    patch_g4f_main()