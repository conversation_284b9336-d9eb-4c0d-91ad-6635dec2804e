# Use the official g4f image as the base
FROM --platform=linux/amd64 hlohaus789/g4f:0.5.1.1

# Arguments
ARG G4F_USER_ID=1200
ARG G4F_USER=seluser
ARG G4F_GROUP_ID=1201 # Adding group ID argument for correct permissions

# Set working directory
WORKDIR /app

# --- Add New Dependencies ---
# Switch to root temporarily to install packages
USER root
# Add Caddy requirements and remove pybase64
RUN apt-get update && apt-get install -y curl debian-keyring debian-archive-keyring apt-transport-https && \
    curl -1sLf 'https://dl.cloudsmith.io/public/caddy/stable/gpg.key' | gpg --dearmor -o /usr/share/keyrings/caddy-stable-archive-keyring.gpg && \
    curl -1sLf 'https://dl.cloudsmith.io/public/caddy/stable/debian.deb.txt' | tee /etc/apt/sources.list.d/caddy-stable.list && \
    apt-get update && \
    apt-get install -y caddy && \
    # Install Python dependencies (NO pybase64) - ensure correct quoting
    pip install --no-cache-dir fastapi 'uvicorn[standard]' 'httpx[http2]' httpx-socks && \
    # Install aiohttp using apt instead of pip to avoid externally managed environment issues
    apt-get update && \
    apt-get install -y python3-aiohttp && \
    # Clean up apt lists
    rm -rf /var/lib/apt/lists/*

# Copy Caddyfile (will be created next)
COPY --chown=root:root Caddyfile /app/Caddyfile

# Switch back to default user immediately after install/copy
USER ${G4F_USER_ID}
# --------------------------

# Copy the custom provider code
COPY --chown=${G4F_USER_ID}:${G4F_GROUP_ID} overwrites/app/g4f/Provider/needs_auth/Chatshare.py /app/g4f/Provider/needs_auth/Chatshare.py
COPY --chown=${G4F_USER_ID}:${G4F_GROUP_ID} overwrites/app/g4f/Provider/needs_auth/ChatshareAccount.py /app/g4f/Provider/needs_auth/ChatshareAccount.py
COPY --chown=${G4F_USER_ID}:${G4F_GROUP_ID} overwrites/app/g4f/Provider/needs_auth/ChatshareRotator.py /app/g4f/Provider/needs_auth/ChatshareRotator.py

# Copy the account rotation system
COPY --chown=${G4F_USER_ID}:${G4F_GROUP_ID} account_rotator /app/account_rotator
COPY --chown=${G4F_USER_ID}:${G4F_GROUP_ID} account_rotator_service.py /app/account_rotator_service.py
USER root
RUN chmod +x /app/account_rotator_service.py
# Create an empty __init__.py file in the account_rotator directory if it doesn't exist
RUN touch /app/account_rotator/__init__.py
# Install the account_rotator module
COPY --chown=root:root setup.py /app/setup.py
# Install the account_rotator module
RUN cd /app && pip install -e . && \
    # Verify the installation
    python3 -c "import account_rotator; print('Account rotator module successfully imported')" || echo "WARNING: Failed to import account_rotator module" && \
    # Verify aiohttp is installed
    python3 -c "import aiohttp; print('aiohttp successfully imported')" || echo "WARNING: Failed to import aiohttp"
USER ${G4F_USER_ID}

# Copy the modification script, ChatShare API script, URL proxy script, and node manager
COPY --chown=${G4F_USER_ID}:${G4F_GROUP_ID} modify_g4f.py /app/modify_g4f.py
COPY --chown=${G4F_USER_ID}:${G4F_GROUP_ID} chatshare_api.py /app/chatshare_api.py
COPY --chown=${G4F_USER_ID}:${G4F_GROUP_ID} url_proxy.py /app/url_proxy.py
COPY --chown=${G4F_USER_ID}:${G4F_GROUP_ID} node_manager.py /app/node_manager.py

# --- Setup Directories ---
# Switch to root temporarily for directory creation and permissions
USER root
RUN mkdir -p /app/har_and_cookies && \
    mkdir -p /app/generated_images && \
    mkdir -p /app/authentication_files && \
    mkdir -p /app/logs && \
    # Create a placeholder auth file (will be replaced by account rotation system)
    echo '{"cookies":{},"headers":{}}' > /app/har_and_cookies/auth_Chatshare.json && \
    # Set ownership first, then permissions
    chown ${G4F_USER_ID}:${G4F_GROUP_ID} -R /app/har_and_cookies && \
    chown ${G4F_USER_ID}:${G4F_GROUP_ID} -R /app/generated_images && \
    chown ${G4F_USER_ID}:${G4F_GROUP_ID} -R /app/authentication_files && \
    chown ${G4F_USER_ID}:${G4F_GROUP_ID} -R /app/logs && \
    # Set very permissive permissions on auth file and directories
    chmod 666 /app/har_and_cookies/auth_Chatshare.json && \
    chmod 777 /app/har_and_cookies && \
    chmod 777 /app/generated_images && \
    chmod 777 /app/authentication_files && \
    chmod 777 /app/logs && \
    # Make sure the logs directory is writable by all users
    chmod -R 777 /app/logs && \
    # Make sure the har_and_cookies directory is writable by all users
    chmod -R 777 /app/har_and_cookies && \
    # Optional: Verify permissions
    ls -ld /app/har_and_cookies /app/generated_images /app/authentication_files /app/logs && \
    ls -l /app/har_and_cookies/auth_Chatshare.json
# Switch back to the non-root user
USER ${G4F_USER_ID}
# ---------------------------------

# --- Add Supervisor Configs ---
# Switch to root temporarily to copy supervisor configs
USER root
COPY --chown=root:root chatshare_api.conf /etc/supervisor/conf.d/chatshare_api.conf
COPY --chown=root:root caddy_proxy.conf /etc/supervisor/conf.d/caddy_proxy.conf
COPY --chown=root:root url_proxy.conf /etc/supervisor/conf.d/url_proxy.conf
COPY --chown=root:root overwrites/etc/supervisor/conf.d/account_rotator.conf /etc/supervisor/conf.d/account_rotator.conf
# Switch back to the non-root user
USER ${G4F_USER_ID}
# -----------------------------

# Run the modification script (This will generate start_services.py)
# Run as the target user to ensure file ownership is correct if script creates files
RUN python3 /app/modify_g4f.py || { echo "Modification script failed!"; exit 1; }

# Optional: Clean up the modification script
RUN rm /app/modify_g4f.py

# Expose the ports for the ChatShare API, URL Masking Proxy, and URL Proxy Service
EXPOSE 8081
EXPOSE 8082
EXPOSE 8083

# IMPORTANT: No CMD or ENTRYPOINT is set.
# The container must be run with a command to start the services, e.g.:
# docker run <image_name> python /app/start_services.py
# Or use a process manager like Supervisor external to this Dockerfile.

# The final USER is already set by the base image or previous steps
# USER ${G4F_USER_ID}