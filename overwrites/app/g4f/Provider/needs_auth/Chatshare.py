# /app/g4f/Provider/needs_auth/Chatshare.py
from __future__ import annotations

import os
import re
import asyncio
import uuid
import json
import base64
import time
import random
import requests # Used for synchronous get_models and as fallback for translation
from urllib.parse import quote
from typing import AsyncIterator, Optional, Generator, Dict, List, Union
from copy import copy
from functools import partial # Import partial

# Ensure nodriver imports are present for the helper
try:
    import nodriver
    from nodriver.core.tab import Tab as NoDriverTab # Specific type hint
    has_nodriver = True
except ImportError:
    NoDriverTab = None # Define as None if nodriver is not installed
    has_nodriver = False

from aiohttp import ClientSession, BaseConnector

# Try to import node_manager, with fallback to default node
try:
    from node_manager import node_manager
    USE_NODE_MANAGER = True
except ImportError:
    USE_NODE_MANAGER = False

from ..base_provider import AsyncAuthedProvider, ProviderModelMixin
from ...typing import AsyncResult, Messages, Cookies, MediaListType
# Use g4f's request wrappers which handle both curl_cffi and aiohttp internally
from ...requests import StreamSession, FormData, get_args_from_nodriver
# Alias aiohttp's response for clarity where needed
from ...requests.aiohttp import StreamResponse as AiohttpStreamResponse
from ...requests.raise_for_status import raise_for_status
from ...image import ImageRequest, to_image, to_bytes, is_accepted_format
from ...errors import MissingAuthError, NoValidHarFileError, ResponseError, MissingRequirementsError, ModelNotSupportedError # Added ModelNotSupportedError
from ...providers.response import JsonConversation, FinishReason, SynthesizeData, AuthResult, ImageResponse, Sources, TitleGeneration, Reasoning, ImagePreview, RequestLogin
from ...tools.media import merge_media
from ..helper import format_cookies, get_last_user_message, get_connector # Import get_connector
from ..openai.har_file import get_har_files, get_headers
# Import the Class instead
from .OpenaiChat import OpenaiChat, Conversation # Reuse Conversation structure & methods
from ... import debug

# Specific constants for Chatshare
DEFAULT_NODE = "sass-node3.chatshare.biz"
CHATSHARE_API_BASE_URL = f"https://{DEFAULT_NODE}"  # Will be updated dynamically if node_manager is available
CHATSHARE_BASE_URL = "https://chatshare.biz"
CHATSHARE_API_PATH = "/backend-api/conversation"
CHATSHARE_MODELS_PATH = "/backend-api/models"
CHATSHARE_FILES_PATH = "/backend-api/files"
CHATSHARE_SYNTHESIZE_PATH = "/backend-api/synthesize"

DEFAULT_HEADERS_MIRROR = {
    "accept": "*/*", "accept-encoding": "gzip, deflate, br, zstd",
    'accept-language': 'en-US,en;q=0.9', "origin": "https://chatshare.biz",
    "pragma": "no-cache", "referer": "https://chatshare.biz/",
    "sec-ch-ua": '"Not:A-Brand";v="24", "Chromium";v="134"', "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": '"macOS"', "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors", "sec-fetch-site": "same-origin",
    "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}

UPLOAD_HEADERS_MIRROR = {
    "accept": "application/json, text/plain, */*", 'accept-language': 'en-US,en;q=0.8',
    "referer": "https://chatshare.biz/", "priority": "u=1, i",
    "sec-ch-ua": '"Not:A-Brand";v="24", "Chromium";v="134"', "sec-ch-ua-mobile": "?0",
    'sec-ch-ua-platform': '"macOS"', "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors", "sec-fetch-site": "cross-site",
    "x-ms-blob-type": "BlockBlob", "x-ms-version": "2020-04-08",
    "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}

# --- Helper function for nodriver selector waiting ---
async def wait_for_any_selector(page: NoDriverTab, selectors: List[str], total_timeout: int = 120):
    """Tries selectors sequentially until one is found or timeout occurs."""
    start_time = time.time()
    last_error = None
    for selector in selectors:
        remaining_timeout = total_timeout - (time.time() - start_time)
        if remaining_timeout <= 0:
            break
        individual_timeout_ms = max(1000, min(10000, int(remaining_timeout * 1000))) # Wait up to 10s per selector in ms
        debug.log(f"{Chatshare.__name__}: nodriver trying selector '{selector}' with timeout {individual_timeout_ms/1000:.1f}s")
        try:
            await page.wait_for(selector, timeout=individual_timeout_ms)
            await asyncio.sleep(0.2) # Small delay to ensure element is interactable if needed
            if await page.query_selector(selector):
                debug.log(f"{Chatshare.__name__}: nodriver found selector '{selector}'")
                return True
        except Exception as e:
             last_error = e
             debug.log(f"{Chatshare.__name__}: nodriver selector '{selector}' error: {type(e).__name__}: {e}")
             pass # Continue to the next selector
    # If loop finished without finding any selector
    error_message = f"None of the selectors {selectors} found within {total_timeout} seconds."
    if last_error: error_message += f" Last error: {type(last_error).__name__}: {last_error}"
    raise asyncio.TimeoutError(error_message)


# Translation dictionary for image generation progress messages
IMAGE_GENERATION_TRANSLATIONS = {
    "任务正在队列中，请耐心等待...": "Queued...",
    "关键字：": "Prompt: ",
    "关键字：>": "Prompt: ",
    "正在绘制图片中...": "Generating...",
    "进度：": "Progress: ",
    "图片绘制成功": "Complete",
    "任务发生错误：input_moderation，该任务的输入或者输出可能违反了OpenAI的相关服务政策，请重新发起请求或调整提示词进行重试": "Error: Content policy violation. Please try a different prompt.",
}

class ImageTranslator:
    """Handles translation of Chinese image generation progress messages to English"""
    def __init__(self):
        self.translation_cache = {}  # Cache for previously translated strings
        # Compile a regex pattern for emoji removal
        self.emoji_pattern = re.compile(
            "["
            "\U0001F600-\U0001F64F"  # emoticons
            "\U0001F300-\U0001F5FF"  # symbols & pictographs
            "\U0001F680-\U0001F6FF"  # transport & map symbols
            "\U0001F700-\U0001F77F"  # alchemical symbols
            "\U0001F780-\U0001F7FF"  # Geometric Shapes
            "\U0001F800-\U0001F8FF"  # Supplemental Arrows-C
            "\U0001F900-\U0001F9FF"  # Supplemental Symbols and Pictographs
            "\U0001FA00-\U0001FA6F"  # Chess Symbols
            "\U0001FA70-\U0001FAFF"  # Symbols and Pictographs Extended-A
            "\*********-\U000027B0"  # Dingbats
            "\U000024C2-\U0000257F"  # Enclosed characters
            "\*********-\U000026FF"  # Miscellaneous Symbols
            "\U00002700-\U000027BF"  # Dingbats
            "\U0000FE00-\U0000FE0F"  # Variation Selectors
            "\U0001F900-\U0001F9FF"  # Supplemental Symbols and Pictographs
            "\U00002300-\U000023FF"  # Miscellaneous Technical
            "\U00002B00-\U00002BFF"  # Miscellaneous Symbols and Arrows
            "\U00003000-\U0000303F"  # CJK Symbols and Punctuation
            "\U0000FE00-\U0000FE0F"  # Variation Selectors
            "]",
            flags=re.UNICODE
        )

    def remove_emojis(self, text):
        """Remove emojis from text"""
        return self.emoji_pattern.sub(r'', text)

    def is_chinese(self, text):
        """Check if text contains Chinese characters"""
        return bool(re.search(r'[\u4e00-\u9fff]', text))

    def is_progress_message(self, text):
        """Check if this is an image generation progress message"""
        progress_indicators = [
            "任务正在队列中", "正在绘制图片", "进度：",
            "关键字：", "关键字：>"
        ]
        return any(indicator in text for indicator in progress_indicators)

    def is_completion_message(self, text):
        """Check if this is an image generation completion message"""
        return "图片绘制成功" in text

    def is_error_message(self, text):
        """Check if this is an image generation error message"""
        return "任务发生错误" in text

    def check_known_translations(self, text):
        """Check if text contains any known phrases"""
        # First remove any emojis from the text
        text_without_emoji = self.remove_emojis(text)

        # Check for completion message with any text after it (like download links)
        # This needs to be first to catch the full completion message with download link
        if "图片绘制成功" in text_without_emoji:
            return "Complete"

        # Direct matches
        for chinese, english in IMAGE_GENERATION_TRANSLATIONS.items():
            if chinese in text_without_emoji:
                return text_without_emoji.replace(chinese, english)

        # Pattern matches for progress percentage
        progress_match = re.search(r'进度：(\d+\.\d+)%', text_without_emoji)
        if progress_match:
            return f"Progress: {progress_match.group(1)}%"

        # Keyword extraction for prompts - handle both variations
        keyword_match = re.search(r'关键字：>?(.*)', text_without_emoji)
        if keyword_match:
            # Get the prompt text and remove any leading '>' character
            prompt_text = keyword_match.group(1).strip()
            # Remove any '>' character that might be at the beginning of the prompt
            prompt_text = prompt_text.lstrip('>').strip()
            return f"Prompt: {prompt_text}"

        return None

    async def translate_with_google(self, text):
        """Translate text using Google Translate's unofficial API"""
        # Check cache first
        if text in self.translation_cache:
            return self.translation_cache[text]

        try:
            # Use Google Translate's unofficial API with standard requests library
            # We'll use a synchronous request in an executor to avoid blocking
            encoded_text = quote(text)
            url = f"https://translate.googleapis.com/translate_a/single?client=gtx&sl=zh&tl=en&dt=t&q={encoded_text}"

            # Run the request in an executor to avoid blocking the event loop
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: requests.get(url, timeout=10)
            )

            if response.status_code == 200:
                # Extract the translation from the response
                # The response format is a nested array where the first element contains translation segments
                data = response.json()
                translation = ""
                if data and isinstance(data, list) and data[0]:
                    # Concatenate all translation segments
                    for segment in data[0]:
                        if segment and isinstance(segment, list) and segment[0]:
                            translation += segment[0]

                # Cache the result if we got a valid translation
                if translation:
                    self.translation_cache[text] = translation
                    return translation

            # Fallback to direct translation if Google API fails or returns empty result
            return self.check_known_translations(text) or text
        except Exception as e:
            debug.error(f"Translation error: {e}")
            # Fallback to direct translation if exception occurs
            return self.check_known_translations(text) or text

    async def translate(self, text):
        """Main translation method that combines known translations with Google Translate"""
        # Skip translation if not Chinese
        if not self.is_chinese(text):
            # Still remove emojis even if not Chinese
            return self.remove_emojis(text)

        # Try known translations first
        known_translation = self.check_known_translations(text)
        if known_translation:
            return known_translation

        # Fall back to Google translation service
        translated = await self.translate_with_google(text)
        # Remove any emojis from the translated text
        return self.remove_emojis(translated)

class Chatshare(AsyncAuthedProvider, ProviderModelMixin):
    label = "Chatshare"
    url = "https://chatshare.biz"
    cookie_domain = ".chatshare.biz"
    working = True
    use_nodriver = True
    needs_auth = True
    supports_gpt_4 = True
    supports_message_history = True
    supports_system_message = True
    default_model = "gpt-4o"
    models = [] # Will be populated by get_models
    model_aliases = {
        "gpt-4o": "gpt-4o", "gpt-4": "gpt-4", "gpt-4o-mini": "gpt-4o-mini",
        "claude-3.7-sonnet": "Claude-sonnet-3-7", "grok-3": "Grok-3-beta",
        "gemini-2.0-pro": "Gemini-2.0-Pro", "deepseek-v3": "Deepseek-V3-满血版",
        "deepseek-r1": "Deepseek-R1-满血版",
    }
    synthesize_content_type = "audio/aac"

    # Use dynamic API base URL with fallback
    @classmethod
    def get_api_base_url(cls):
        """Get the current API base URL with node manager support."""
        if USE_NODE_MANAGER:
            try:
                return node_manager.get_api_base_url()
            except Exception as e:
                debug.error(f"{cls.__name__}: Error getting API base URL from node manager: {e}")
                return f"https://{DEFAULT_NODE}"
        return f"https://{DEFAULT_NODE}"

    # Class property for _api_base_url
    @classmethod
    @property
    def _api_base_url(cls):
        return cls.get_api_base_url()

    _api_path = CHATSHARE_API_PATH
    _models_path = CHATSHARE_MODELS_PATH
    _files_path = CHATSHARE_FILES_PATH
    _synthesize_path = CHATSHARE_SYNTHESIZE_PATH

    prefix = "cs-" # Define the prefix used in modify_g4f.py

    @classmethod
    def get_models(cls, **kwargs) -> list[str]:
        # Synchronous request for models (as in original)
        if not cls.models:
            try:
                # Use requests for synchronous compatibility if needed elsewhere
                response = requests.get(f"{cls._api_base_url}{cls._models_path}")
                response.raise_for_status()
                data = response.json()
                if "models" in data and isinstance(data["models"], list):
                    cls.models = [model.get("slug") for model in data.get("models", []) if "slug" in model]
                    # Update default model if necessary
                    if cls.models and cls.default_model not in cls.models:
                        available_defaults = [m for m in ["gpt-4o", "gpt-4", "gpt-4o-mini"] if m in cls.models]
                        cls.default_model = available_defaults[0] if available_defaults else cls.models[0]
                    debug.log(f"{cls.__name__}: Successfully fetched {len(cls.models)} models.")
                else:
                    debug.error(f"{cls.__name__}: Unexpected format in models API response: {data}")
                    cls.models = [cls.default_model] # Fallback
            except Exception as e:
                debug.error(f"{cls.__name__}: Failed to fetch models: {type(e).__name__}: {e}")
                cls.models = [cls.default_model] # Fallback
        return cls.models

    # <<< --- OVERRIDDEN get_model METHOD --- >>>
    @classmethod
    def get_model(cls, model: str, **kwargs) -> str:
        """
        Validates and returns the correct model name for the API, handling the 'cs-' prefix.
        """
        # Ensure models are loaded (might trigger the synchronous fetch if first time)
        cls.get_models(**kwargs)

        base_model_name = model
        is_prefixed = model.startswith(cls.prefix)

        if is_prefixed:
            # If the requested model has the prefix, strip it
            base_model_name = model[len(cls.prefix):]
            debug.log(f"{cls.__name__}: Received prefixed model '{model}', using base '{base_model_name}' for internal check.")

        # Use the base name (or original if not prefixed) for validation
        # Check primary model names first
        if base_model_name in cls.models:
            cls.last_model = base_model_name # Store the name the backend expects
            debug.last_model = base_model_name
            return base_model_name

        # Check aliases if primary name not found
        if base_model_name in cls.model_aliases:
            target_model = cls.model_aliases[base_model_name]
            # Ensure the alias target is actually in the supported models list
            if target_model in cls.models:
                cls.last_model = target_model # Store the name the backend expects
                debug.last_model = target_model
                return target_model
            else:
                # This case should ideally not happen if aliases are well-maintained
                 debug.error(f"{cls.__name__}: Alias '{base_model_name}' points to unsupported model '{target_model}'.")

        # If neither the base name nor its alias target (if any) is found
        valid_models = cls.models + list(cls.model_aliases.keys())
        raise ModelNotSupportedError(
            f"Model is not supported: {model} in: {cls.__name__} Valid models: {valid_models}"
        )
    # <<< --- END OVERRIDDEN get_model METHOD --- >>>

    @classmethod
    async def _try_read_har(cls) -> AuthResult:
        """
        Attempts to read authentication details from HAR files.

        Raises:
            NoValidHarFileError: If no suitable HAR entry is found.

        Returns:
            AuthResult: Containing cookies and headers if successful.
        """
        auth_cookies: Cookies = None
        auth_headers: Dict[str, str] = None
        found_entry = False

        for path in get_har_files():
            with open(path, 'rb') as file:
                try:
                    harFile = json.loads(file.read())
                except json.JSONDecodeError:
                    continue # Skip invalid HAR files

                for entry in harFile.get('log', {}).get('entries', []):
                    request_url = entry.get('request', {}).get('url', '')
                    # Check if the entry matches the target URLs
                    if (request_url.startswith(cls._api_base_url + cls._api_path) or
                        request_url.startswith(CHATSHARE_BASE_URL)):

                        # Extract cookies
                        cookies_found = None
                        request_cookies = entry.get('request', {}).get('cookies', [])
                        if request_cookies:
                            cookies_found = {c['name']: c['value'] for c in request_cookies}
                        else:
                            # Fallback to Cookie header if request.cookies is empty/missing
                            cookie_header = next((h['value'] for h in entry.get('request', {}).get('headers', []) if h.get('name', '').lower() == 'cookie'), None)
                            if cookie_header:
                                cookies_found = {c.split('=')[0].strip(): c.split('=', 1)[1].strip() for c in cookie_header.split(';') if '=' in c}

                        # Check for required session cookies
                        if cookies_found and ('token' in cookies_found or 'gfsessionid' in cookies_found):
                            auth_cookies = cookies_found
                            # Extract relevant headers, excluding auth-related ones if present
                            auth_headers = get_headers(entry)
                            auth_headers.pop('authorization', None); auth_headers.pop('Authorization', None) # Ensure no conflicting auth
                            debug.log(f"{cls.__name__}: Found valid cookies/headers in HAR: {path}")
                            found_entry = True
                            break # Found a valid entry in this file
            if found_entry:
                break # Found a valid entry, no need to check other files

        if not found_entry:
            raise NoValidHarFileError(f"No relevant HAR entry for {cls.url} found.")

        # Return successful result
        return AuthResult(cookies=auth_cookies, headers=auth_headers)

    @classmethod
    async def on_auth_async(cls, proxy: str = None, **kwargs) -> AsyncIterator:
        """
        Handles authentication, trying HAR files first, then falling back to nodriver.
        Aligns error handling and fallback logic with OpenaiChat.
        """
        try:
            # Attempt to get credentials from HAR files first
            auth_result = await cls._try_read_har()
            yield auth_result
            return # Successfully authenticated via HAR

        except NoValidHarFileError as e:
            # HAR reading failed (no files or no valid entry found)
            debug.log(f"{cls.__name__}: HAR reading failed: {e}. Checking for nodriver fallback.")

            if not has_nodriver:
                # If nodriver is not available, re-raise the specific HAR error
                raise NoValidHarFileError(f"No relevant HAR entry for {cls.url} found, and nodriver is not installed.") from e

            # Nodriver is available, attempt browser automation
            debug.log(f"{cls.__name__}: Attempting nodriver authentication.")
            yield RequestLogin(cls.label, os.environ.get("G4F_LOGIN_URL") or "") # Inform GUI
            browser = kwargs.get("browser") # Allow passing a browser instance for testing/reuse
            try:
                # Define selectors to wait for, indicating the page/app is ready
                wait_selectors = [
                    'div#prompt-textarea',                # Common textarea IDs/selectors
                    'textarea[placeholder*="询问任何问题"]', # Placeholder text in Chinese
                    'textarea[placeholder*="Send a message"]', # Placeholder text in English
                    'div.ProseMirror',                    # A common editor class
                    'form[data-type="unified-composer"]', # A form structure often used
                    'textarea',                           # A generic textarea fallback
                ]
                timeout = 120 # Define timeout for waiting

                # Call the helper to get args (cookies, headers) via nodriver
                args = await get_args_from_nodriver(
                    cls.url,
                    proxy=proxy,
                    callback=partial(wait_for_any_selector, selectors=wait_selectors, total_timeout=timeout),
                    timeout=timeout + 10, # Add buffer to overall timeout
                    browser=browser
                )
                # Yield the successful nodriver authentication result
                yield AuthResult(cookies=args.get("cookies"), headers=args.get("headers"))

            except Exception as nodriver_error:
                # Handle potential errors during nodriver execution
                debug.error(f"{cls.__name__}: nodriver automation failed.")
                # Raise a more appropriate error indicating authentication failure
                raise MissingAuthError(f"nodriver automation failed for {cls.__name__}: {type(nodriver_error).__name__}: {nodriver_error}") from nodriver_error

    # Keep the existing methods below unchanged
    @classmethod
    async def upload_images(cls, session: StreamSession, auth_result: AuthResult, media: MediaListType) -> List[ImageRequest]:
        # Reusing OpenaiChat's logic as it seems compatible
        return await OpenaiChat.upload_images(session, auth_result, media)

    @classmethod
    async def get_generated_image(cls, session: StreamSession, auth_result: AuthResult, element: dict, prompt: str = None) -> ImageResponse:
        """Get generated image and save it to the generated_images folder"""
        try:
            # Extract prompt and file_id from the element
            prompt = element["metadata"]["dalle"]["prompt"]
            file_id = element["asset_pointer"].split("file-service://", 1)[1]
        except TypeError:
            return None
        except Exception as e:
            raise RuntimeError(f"No Image: {e.__class__.__name__}: {e}")

        try:
            # Get the download URL
            async with session.get(f"{OpenaiChat.url}/backend-api/files/{file_id}/download", headers=auth_result.headers) as response:
                await raise_for_status(response)
                download_url = (await response.json())["download_url"]

                # Create the generated_images directory if it doesn't exist
                import os
                from datetime import datetime
                images_dir = "/app/generated_images"
                os.makedirs(images_dir, exist_ok=True)

                # Generate a filename based on timestamp and prompt
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                # Clean the prompt to create a valid filename
                clean_prompt = "".join(c if c.isalnum() or c in " -_" else "_" for c in prompt[:50])
                clean_prompt = clean_prompt.strip().replace(" ", "_")
                filename = f"{timestamp}_{clean_prompt}.png"
                filepath = os.path.join(images_dir, filename)

                # Download and save the image
                debug.log(f"{cls.__name__}: Downloading image to {filepath}")
                async with session.get(download_url) as img_response:
                    await raise_for_status(img_response)
                    image_data = await img_response.read()

                    # Save the image to the generated_images folder
                    with open(filepath, "wb") as f:
                        f.write(image_data)
                    debug.log(f"{cls.__name__}: Image saved to {filepath}")

                # Return the ImageResponse with both the URL and local path
                # Use English alt text instead of Chinese "图片"
                alt_text = "Generated Image" if "图片" in prompt else prompt
                return ImageResponse(download_url, alt_text)
        except Exception as e:
            debug.error(f"{cls.__name__}: Error downloading/saving image: {e}")
            # Still return the ImageResponse with the URL even if saving fails
            # Use English alt text instead of Chinese "图片"
            alt_text = "Generated Image" if 'download_url' in locals() and "图片" in prompt else prompt
            return ImageResponse(download_url, alt_text) if 'download_url' in locals() else None

    @classmethod
    async def synthesize(cls, params: dict, auth_result: AuthResult, proxy: str = None) -> AsyncIterator[bytes]:
        # Reusing OpenaiChat's logic for synthesis endpoint
        headers = {**DEFAULT_HEADERS_MIRROR, **auth_result.headers, "Accept": "*/*", "Cookie": format_cookies(auth_result.cookies)}
        headers.pop('content-length', None) # Remove potentially problematic headers
        async with StreamSession(impersonate="chrome", timeout=0, proxy=proxy, cookies=auth_result.cookies, headers=headers) as session:
            async with session.get(f"{cls._api_base_url}{cls._synthesize_path}", params=params) as response:
                await raise_for_status(response)
                while not response.content.at_eof():
                    chunk = await response.content.readany()
                    if not chunk: break
                    yield chunk

    @classmethod
    async def iter_messages_line(cls, session: StreamSession, auth_result: AuthResult, line: bytes, fields: Conversation, sources: Sources) -> AsyncIterator:
        """Custom implementation of iter_messages_line to fix the 'None' string issue"""
        if not line.startswith(b"data: "):
            return
        elif line.startswith(b"data: [DONE]"):
            return
        try:
            line = json.loads(line[6:])
        except:
            return
        if not isinstance(line, dict):
            return
        if "type" in line:
            if line["type"] == "title_generation":
                yield TitleGeneration(line["title"])
        fields.p = line.get("p", fields.p)
        if fields.p and fields.p.startswith("/message/content/thoughts"):
            if fields.p.endswith("/content"):
                if fields.thoughts_summary:
                    yield Reasoning(token="", status=fields.thoughts_summary)
                    fields.thoughts_summary = ""
                token = line.get("v")
                if token and token != "None":
                    yield Reasoning(token=token)
            elif fields.p.endswith("/summary"):
                fields.thoughts_summary += line.get("v", "")
            return
        if "v" in line:
            v = line.get("v")
            if isinstance(v, str) and fields.recipient == "all":
                if "p" not in line or line.get("p") == "/message/content/parts/0":
                    if fields.is_thinking:
                        if v and v != "None":
                            yield Reasoning(token=v)
                    elif v and v != "None":
                        yield v
            elif isinstance(v, list):
                for m in v:
                    if m.get("p") == "/message/content/parts/0" and fields.recipient == "all":
                        value = m.get("v")
                        if value and value != "None":
                            yield value
                    elif m.get("p") == "/message/metadata/search_result_groups":
                        for entry in [p.get("entries") for p in m.get("v")]:
                            for link in entry:
                                sources.add_source(link)
                    elif m.get("p") == "/message/metadata/content_references":
                        for entry in m.get("v"):
                            for link in entry.get("sources", []):
                                sources.add_source(link)
                    elif m.get("p") and re.match(r"^/message/metadata/content_references/\d+$", m.get("p")):
                        sources.add_source(m.get("v"))
                    elif m.get("p") == "/message/metadata/finished_text":
                        fields.is_thinking = False
                        yield Reasoning(status=m.get("v"))
                    elif m.get("p") == "/message/metadata" and fields.recipient == "all":
                        fields.finish_reason = m.get("v", {}).get("finish_details", {}).get("type")
                        break
            elif isinstance(v, dict):
                if fields.conversation_id is None:
                    fields.conversation_id = v.get("conversation_id")
                    debug.log(f"{cls.__name__}: New conversation: {fields.conversation_id}")
                m = v.get("message", {})
                fields.recipient = m.get("recipient", fields.recipient)
                if fields.recipient == "all":
                    c = m.get("content", {})
                    if c.get("content_type") == "text" and m.get("author", {}).get("role") == "tool" and "initial_text" in m.get("metadata", {}):
                        fields.is_thinking = True
                        yield Reasoning(status=m.get("metadata", {}).get("initial_text"))
                    if c.get("content_type") == "multimodal_text":
                        generated_images = []
                        for element in c.get("parts"):
                            if isinstance(element, dict) and element.get("content_type") == "image_asset_pointer":
                                image = cls.get_generated_image(session, auth_result, element)
                                generated_images.append(image)
                        for image_response in await asyncio.gather(*generated_images):
                            if image_response is not None:
                                yield image_response
                    if m.get("author", {}).get("role") == "assistant":
                        if fields.parent_message_id is None:
                            fields.parent_message_id = v.get("message", {}).get("id")
                        fields.message_id = v.get("message", {}).get("id")
            return
        if "error" in line and line.get("error"):
            raise RuntimeError(line.get("error"))

    @classmethod
    async def create_authed(
        cls, model: str, messages: Messages, auth_result: AuthResult, proxy: str = None, timeout: int = 180,
        action: str = "next", conversation: Conversation = None, media: MediaListType = None,
        return_conversation: bool = False, web_search: bool = False, **kwargs
    ) -> AsyncResult:
        # This method remains largely the same as it reuses OpenaiChat's logic for request/response
        async with StreamSession(
            proxy=proxy, impersonate="chrome", timeout=timeout, cookies=auth_result.cookies
        ) as session:
            image_requests = None
            if media:
                try: image_requests = await cls.upload_images(session, auth_result, merge_media(media, messages))
                except Exception as e: debug.error(f"{cls.__name__}: Upload image failed: {e}"); image_requests = None

            # Use the validated/mapped model name returned by get_model
            # Do NOT use the potentially prefixed name here
            backend_model_name = cls.get_model(model)

            if conversation is None: conversation = Conversation(None, str(uuid.uuid4()))
            else: conversation = copy(conversation)

            if action is None or action == "variant": action = "next"

            data = {
                "action": action,
                "messages": OpenaiChat.create_messages(messages, image_requests, ["search"] if web_search else None),
                "parent_message_id": getattr(conversation, "parent_message_id", getattr(conversation, "message_id", str(uuid.uuid4()))),
                "model": backend_model_name, # Send the unprefixed name to the backend
                "timezone_offset_min": -120, "timezone": "Europe/Berlin",
                "conversation_mode": {"kind": "primary_assistant"}, "enable_message_followups": True,
                "system_hints": ["search"] if web_search else None, "supports_buffering": True,
                "supported_encodings": ["v1"], "client_contextual_info": {
                    "is_dark_mode":False, "time_since_loaded":random.randint(20, 500),
                    "page_height":578,"page_width":1850,"pixel_ratio":1,"screen_height":1080,"screen_width":1920
                 },
                "paragen_cot_summary_display_override": "allow", "force_paragen": False,
                "force_paragen_model_slug": "", "force_nulligen": False, "force_rate_limit": False,
                "reset_rate_limits": False, "history_and_training_disabled": False,
                "websocket_request_id": str(uuid.uuid4())
            }
            if conversation.conversation_id is not None: data["conversation_id"] = conversation.conversation_id
            else: data["parent_message_id"] = str(uuid.uuid4()) # Ensure a valid parent ID for new conversations

            # Prepare headers, ensuring cookie formatting and removing conflicting headers
            headers = {
                **DEFAULT_HEADERS_MIRROR, # Use the mirrored headers specific to Chatshare
                **(auth_result.headers or {}), # Include headers from auth (might contain User-Agent etc.)
                "accept": "text/event-stream",
                "content-type": "application/json",
                "cookie": format_cookies(auth_result.cookies), # Format cookies correctly
            }

            # Ensure User-Agent is set
            if "user-agent" not in headers and "User-Agent" not in headers:
                headers["user-agent"] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"

            # Clean up headers that might cause issues
            headers.pop('content-length', None); headers.pop('host', None); headers.pop(':authority', None)
            headers.pop(':method', None); headers.pop(':path', None); headers.pop(':scheme', None)
            headers.pop('authorization', None) # Remove any lingering Authorization header

            # Log the headers for debugging
            debug.log(f"{cls.__name__}: Request headers: {headers}")

            api_url = f"{cls._api_base_url}{cls._api_path}"

            # Use g4f's StreamResponse which adapts to curl_cffi/aiohttp
            from ...requests import StreamResponse # Make sure StreamResponse is imported

            # Initialize translator if this is the 4o-image model
            is_4o_image_model = model in ["4o-image", "cs-4o-image"]
            translator = ImageTranslator() if is_4o_image_model else None
            in_image_progress = False

            response: StreamResponse # Type hint for clarity
            async with session.post(api_url, json=data, headers=headers) as response:
                await raise_for_status(response, f"{cls.__name__}: API request failed")

                sources = Sources([])
                _in_reasoning = False
                # Use the response directly with iter_messages_line
                try:
                    async for line in response.iter_lines(): # Use the correct method for the wrapper
                        # line is bytes, pass it directly to the parser
                        async for chunk in cls.iter_messages_line(session, auth_result, line, conversation, sources):
                            # Special handling for 4o-image model's Chinese progress messages
                            if is_4o_image_model and isinstance(chunk, str) and translator.is_chinese(chunk):
                                # Determine if this is a progress message, completion message, or error message
                                is_progress = translator.is_progress_message(chunk)
                                is_completion = translator.is_completion_message(chunk)
                                is_error = translator.is_error_message(chunk)

                                # Handle progress messages
                                if is_progress:
                                    # Start progress section if not already in it
                                    if not in_image_progress:
                                        in_image_progress = True
                                        # Start with a clean thinking tag
                                        yield "<think>"

                                    # Translate and yield the progress message
                                    translated = await translator.translate(chunk)

                                    # Format the message based on the translation
                                    formatted_message = ""
                                    # Use the new format with arrow and proper spacing
                                    if "Queued" in translated:
                                        formatted_message = "→ Request received"
                                    elif "Prompt" in translated:
                                        # Extract the prompt text and clean it by removing any '>' character
                                        prompt_text = translated.replace("Prompt: ", "")
                                        # Remove any '>' character that might be at the beginning of the prompt
                                        prompt_text = prompt_text.lstrip('>').strip()
                                        # Add backticks around the prompt text for better visual separation
                                        formatted_message = f"→ Prompt: `{prompt_text}`"
                                    elif "Generating" in translated:
                                        formatted_message = "→ Generating…"
                                    elif "Progress" in translated:
                                        # Extract percentage for progress messages
                                        percentage = re.search(r'Progress: (\d+\.\d+)%', translated)
                                        if percentage:
                                            # Use the new format: "→ Progress percentage: ​ 17.5"
                                            # Note the thin space (\u200b) after the colon
                                            formatted_message = f"→ Progress percentage: \u200b {percentage.group(1)}"
                                        else:
                                            # Fallback if regex fails
                                            progress_value = translated.replace("Progress: ", "")
                                            # Remove the % sign if present
                                            progress_value = progress_value.replace("%", "")
                                            formatted_message = f"→ Progress percentage: \u200b {progress_value}"
                                    else:
                                        # For any other message, use the new format
                                        formatted_message = f"→ {translated}"

                                    # Ensure each status message is on its own line with proper spacing
                                    # Add a newline before the message to ensure separation
                                    yield f"\n{formatted_message}\n"
                                    continue

                                # Handle completion or error messages
                                elif is_completion or is_error:
                                    # End progress section if we were in it
                                    if in_image_progress:
                                        # For completion, add a 100% progress entry before closing the tag
                                        if is_completion:
                                            # Add the 100% message with the new format
                                            yield "\n→ Progress percentage: \u200b 100\n"
                                            # Add the "Generation complete" message with extra newlines
                                            yield "\n→ Generation complete.\n\n\n"
                                        in_image_progress = False
                                        # Close the thinking tag
                                        yield "</think>"

                                    # For completion messages, don't yield any text (just let the image display)
                                    if is_completion:
                                        # Don't yield anything - the image will display on its own
                                        pass
                                    else:
                                        # For error messages, translate and format as plain text
                                        translated = await translator.translate(chunk)
                                        # Format error message
                                        if "Error:" in translated:
                                            error_parts = translated.split("Error:", 1)
                                            if len(error_parts) > 1:
                                                error_msg = f"Error: {error_parts[1].strip()}"
                                            else:
                                                error_msg = translated
                                        else:
                                            error_msg = f"Error: {translated}"
                                        yield error_msg
                                    continue

                            # Regular handling for non-image progress messages
                            if isinstance(chunk, Reasoning):
                                # only stream actual tokens, skip thinking markers
                                if chunk.token:
                                    if not _in_reasoning:
                                        _in_reasoning = True
                                        # Start with a clean thinking tag
                                        yield "<think>"
                                    # Only yield non-None tokens
                                    if chunk.token != "None":
                                        # Ensure tokens are properly separated with spaces
                                        # Check if the token is a complete word or part of a word
                                        token = chunk.token
                                        # Add a space before the token if it's a complete word and not punctuation
                                        if token.strip() and not token.strip() in ".,;:!?()[]{}":
                                            # Only add space if it's a new word (not continuing a previous word)
                                            if token[0].isalnum() and not token[0].isspace():
                                                token = " " + token.lstrip()
                                        # Preserve the token with proper spacing
                                        yield token
                                continue
                            else:
                                if _in_reasoning:
                                    # Close the thinking tag
                                    yield "</think>"
                                    _in_reasoning = False
                                # Only yield non-None chunks
                                if chunk is not None and str(chunk) != "None":
                                    yield chunk
                        # Check conversation state after processing line
                        if conversation.finish_reason is not None:
                            break
                except asyncio.TimeoutError:
                    debug.error(f"{cls.__name__}: Timeout reading stream line.")
                except ConnectionResetError:
                    debug.error(f"{cls.__name__}: Connection reset while reading stream.")
                except Exception as e:
                    debug.error(f"{cls.__name__}: Error reading/parsing stream: {type(e).__name__}: {e}")
                    if conversation.finish_reason is None: yield FinishReason("stop") # Assume stop
                    else: yield FinishReason(conversation.finish_reason)
                    return # Stop gracefully

                # Ensure final state is yielded
                if sources.list: yield sources
                if return_conversation: yield conversation

                if conversation.finish_reason is None: yield FinishReason("stop")
                else: yield FinishReason(conversation.finish_reason)

                # Make sure to close any open tags
                if in_image_progress:
                    # Close the thinking tag
                    yield "</think>"
                    in_image_progress = False

                # flush any remaining open think tag
                if _in_reasoning:
                    # Close the thinking tag
                    yield "</think>"
                    _in_reasoning = False