#!/usr/bin/env python3
"""
ChatshareRotator provider that integrates with the account rotation system.
"""
from __future__ import annotations

import os
import sys
import logging
from typing import Optional, Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Try to import account_rotator directly first, then fall back to path manipulation
try:
    # Try direct import first (assuming it's in Python path)
    from account_rotator import account_rotator
    ACCOUNT_ROTATOR_AVAILABLE = True
except ImportError:
    try:
        # Add the parent directory to the path to import account_rotator
        sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))))
        from account_rotator import account_rotator
        ACCOUNT_ROTATOR_AVAILABLE = True
    except ImportError:
        try:
            # Try absolute path in Docker container
            sys.path.insert(0, "/app")
            from account_rotator import account_rotator
            ACCOUNT_ROTATOR_AVAILABLE = True
        except ImportError:
            logger.warning("Could not import account_rotator module. Account rotation will be disabled.")
            ACCOUNT_ROTATOR_AVAILABLE = False

from ...typing import AsyncResult, Messages, MediaListType
from ...providers.response import AuthResult
from ... import debug
from .Chatshare import Chatshare

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ChatshareRotator(Chatshare):
    """Chatshare provider that integrates with the account rotation system."""

    parent = "Chatshare"
    label = "ChatshareRotator"
    needs_auth = True
    use_nodriver = False

    # Override the model_aliases to ensure proper mapping
    model_aliases = {
        **Chatshare.model_aliases,
        # Add explicit mappings for Chinese model names
        "grok-3-推理模型": "Grok-3-beta",
        "Grok-3-推理模型": "Grok-3-beta",
    }

    @classmethod
    async def _record_success(cls):
        """Helper method to record success with error handling."""
        try:
            current_account = account_rotator.get_current_account()
            if current_account:
                account_id = current_account.get("id")
                if account_id:
                    debug.log(f"{cls.__name__}: Recording success for account {account_id}")
                    try:
                        await account_rotator.record_success(account_id)
                    except Exception as success_error:
                        debug.error(f"{cls.__name__}: Error recording success: {success_error}")
        except Exception as e:
            debug.error(f"{cls.__name__}: Error in success recording helper: {e}")

    @classmethod
    async def _record_failure(cls):
        """Helper method to record failure with error handling."""
        try:
            current_account = account_rotator.get_current_account()
            if current_account:
                account_id = current_account.get("id")
                if account_id:
                    debug.log(f"{cls.__name__}: Recording failure for account {account_id}")
                    try:
                        await account_rotator.record_failure(account_id)
                    except Exception as failure_error:
                        debug.error(f"{cls.__name__}: Error recording failure: {failure_error}")
        except Exception as e:
            debug.error(f"{cls.__name__}: Error in failure recording helper: {e}")

    @classmethod
    async def _handle_account_rotation_and_request(cls) -> Optional[Dict[str, Any]]:
        """
        Helper method to handle account rotation and request recording with proper error handling.

        Returns:
            Optional[Dict[str, Any]]: The current account after rotation, or None if unavailable
        """
        try:
            # Check and rotate if needed
            rotation_success = False
            try:
                rotation_success = await account_rotator.check_and_rotate_if_needed()
            except Exception as rotation_error:
                debug.error(f"{cls.__name__}: Error during account rotation: {rotation_error}")
                # Continue despite rotation errors

            # Get the current account after rotation attempt
            current_account = None
            try:
                current_account = account_rotator.get_current_account()
            except Exception as account_error:
                debug.error(f"{cls.__name__}: Error getting current account: {account_error}")
                # Try to continue despite errors

            # Log the result
            if rotation_success:
                debug.log(f"{cls.__name__}: Account rotation successful or not needed")
            else:
                debug.warning(f"{cls.__name__}: Account rotation failed or had errors")
                # Check if current account is rate limited
                if current_account and current_account.get("id"):
                    account_id = current_account.get("id")
                    try:
                        is_limited = account_rotator.is_rate_limited(account_id)
                        if is_limited:
                            debug.warning(f"{cls.__name__}: Using rate-limited account {account_id} because rotation failed")
                    except Exception as limit_error:
                        debug.error(f"{cls.__name__}: Error checking if account is rate limited: {limit_error}")

            # Record the request for the current account
            if current_account:
                account_id = current_account.get("id")
                if account_id:
                    debug.log(f"{cls.__name__}: Recording request for account {account_id}")
                    try:
                        await account_rotator.record_request(account_id)
                    except Exception as request_error:
                        debug.error(f"{cls.__name__}: Error recording request: {request_error}")
                        # Continue with the request even if recording fails

            return current_account
        except Exception as e:
            debug.error(f"{cls.__name__}: Unhandled error in account rotation helper: {e}")
            # Try to get current account as a last resort
            try:
                return account_rotator.get_current_account()
            except Exception:
                # Return None if all else fails
                return None

    @classmethod
    async def create_authed(
        cls, model: str, messages: Messages, auth_result: AuthResult, proxy: str = None, timeout: int = 180,
        action: str = "next", conversation = None, media: MediaListType = None,
        return_conversation: bool = False, web_search: bool = False, **kwargs
    ) -> AsyncResult:
        try:
            # Log the model being used
            debug.log(f"{cls.__name__}: Using model '{model}'")

            # Check if current account is rate limited and rotate if needed
            if ACCOUNT_ROTATOR_AVAILABLE:
                try:
                    # Use helper method for async operations with proper error handling
                    await cls._handle_account_rotation_and_request()
                except Exception as rotation_error:
                    debug.error(f"{cls.__name__}: Error during account rotation check: {rotation_error}")
                    # Continue with the request even if rotation check fails

            # Special handling for Chinese model names
            if model == "Grok-3-推理模型" or model == "grok-3-推理模型":
                debug.log(f"{cls.__name__}: Converting Chinese model name '{model}' to 'Grok-3-beta'")
                model = "Grok-3-beta"
            elif model == "4o":
                debug.log(f"{cls.__name__}: Converting shorthand model name '4o' to 'gpt-4o'")
                model = "gpt-4o"

            # Ensure auth_result has proper structure
            if not auth_result or not isinstance(auth_result, AuthResult):
                debug.error(f"{cls.__name__}: Invalid auth_result: {type(auth_result)}")
                auth_result = AuthResult(cookies={}, headers={})

            # Ensure headers has required fields
            if not auth_result.headers:
                auth_result.headers = {}

            # Add required headers if missing
            if "user-agent" not in auth_result.headers and "User-Agent" not in auth_result.headers:
                auth_result.headers["user-agent"] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"

            # Ensure content-type is set correctly
            auth_result.headers["content-type"] = "application/json"

            # Ensure accept header is set correctly
            auth_result.headers["accept"] = "text/event-stream"

            # Ensure origin and referer are set correctly
            auth_result.headers["origin"] = "https://chatshare.biz"
            auth_result.headers["referer"] = "https://chatshare.biz/"

            # Remove any problematic headers
            for header in [':authority', ':method', ':path', ':scheme', 'host', 'content-length']:
                if header in auth_result.headers:
                    auth_result.headers.pop(header)

            # Log the auth_result structure
            debug.log(f"{cls.__name__}: Auth result headers keys: {list(auth_result.headers.keys())}")
            debug.log(f"{cls.__name__}: Auth result cookies keys: {list(auth_result.cookies.keys()) if auth_result.cookies else 'None'}")

            # Log the full headers for debugging
            debug.log(f"{cls.__name__}: Full headers: {auth_result.headers}")

            # Call the parent class's create_authed method
            parent_generator = super().create_authed(
                model=model, messages=messages, auth_result=auth_result, proxy=proxy, timeout=timeout,
                action=action, conversation=conversation, media=media,
                return_conversation=return_conversation, web_search=web_search, **kwargs
            )

            # Track if we've received any content
            received_content = False

            # Yield each item from the parent generator
            async for item in parent_generator:
                # Skip 'None' strings
                if item is not None and str(item) != "None":
                    received_content = True
                    yield item

            # Record success if we received content
            if ACCOUNT_ROTATOR_AVAILABLE and received_content:
                await cls._record_success()

        except Exception as e:
            # Record failure in account rotator if available
            if ACCOUNT_ROTATOR_AVAILABLE:
                await cls._record_failure()

            # Enhanced error logging
            debug.error(f"{cls.__name__}: Error in create_authed: {type(e).__name__}: {e}")

            # Check for specific errors
            error_str = str(e)

            # Handle 405 Method Not Allowed error
            if "405" in error_str or "Method Not Allowed" in error_str:
                debug.error(f"{cls.__name__}: Detected 405 Method Not Allowed error. This may indicate an issue with the auth file or API endpoint.")

                # Try to get the current account and log details
                try:
                    current_account = account_rotator.get_current_account()
                    if current_account:
                        account_id = current_account.get("id")
                        debug.error(f"{cls.__name__}: Current account ID: {account_id}")

                        # Force rotation to try a different account
                        debug.log(f"{cls.__name__}: Forcing account rotation due to 405 error")
                        rotation_success, new_account = await account_rotator.rotate_account()

                        if rotation_success and new_account:
                            debug.log(f"{cls.__name__}: Successfully rotated to account {new_account.get('id')} after 405 error")
                        else:
                            debug.error(f"{cls.__name__}: Failed to rotate account after 405 error")
                except Exception as rotation_error:
                    debug.error(f"{cls.__name__}: Error during forced rotation after 405 error: {rotation_error}")

            # Handle 400 Bad Request error
            elif "400" in error_str or "Bad Request" in error_str:
                debug.error(f"{cls.__name__}: Detected 400 Bad Request error. This may indicate an issue with the request format or authentication.")

                # Try to get the current account and log details
                try:
                    current_account = account_rotator.get_current_account()
                    if current_account:
                        account_id = current_account.get("id")
                        debug.error(f"{cls.__name__}: Current account ID: {account_id}")

                        # Force rotation to try a different account
                        debug.log(f"{cls.__name__}: Forcing account rotation due to 400 error")
                        rotation_success, new_account = await account_rotator.rotate_account()

                        if rotation_success and new_account:
                            debug.log(f"{cls.__name__}: Successfully rotated to account {new_account.get('id')} after 400 error")
                        else:
                            debug.error(f"{cls.__name__}: Failed to rotate account after 400 error")
                except Exception as rotation_error:
                    debug.error(f"{cls.__name__}: Error during forced rotation after 400 error: {rotation_error}")

            # Handle other API errors
            elif "API request failed" in error_str:
                debug.error(f"{cls.__name__}: Detected API request failure. This may indicate an issue with the API endpoint or authentication.")

                # Try to get the current account and log details
                try:
                    current_account = account_rotator.get_current_account()
                    if current_account:
                        account_id = current_account.get("id")
                        debug.error(f"{cls.__name__}: Current account ID: {account_id}")

                        # Force rotation to try a different account
                        debug.log(f"{cls.__name__}: Forcing account rotation due to API request failure")
                        rotation_success, new_account = await account_rotator.rotate_account()

                        if rotation_success and new_account:
                            debug.log(f"{cls.__name__}: Successfully rotated to account {new_account.get('id')} after API request failure")
                        else:
                            debug.error(f"{cls.__name__}: Failed to rotate account after API request failure")
                except Exception as rotation_error:
                    debug.error(f"{cls.__name__}: Error during forced rotation after API request failure: {rotation_error}")

            # Re-raise the exception
            raise
