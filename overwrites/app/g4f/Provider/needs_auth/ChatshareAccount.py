# /app/g4f/Provider/needs_auth/ChatshareAccount.py
from __future__ import annotations

from ...typing import AsyncResult, Messages, MediaListType
from ...providers.response import AuthResult
from ... import debug
from .Chatshare import Chatshare

class ChatshareAccount(Chatshare):
    parent = "Chatshare"
    needs_auth = True
    use_nodriver = False

    # Override the model_aliases to ensure proper mapping
    model_aliases = {
        **Chatshare.model_aliases,
        # Add explicit mappings for Chinese model names
        "grok-3-推理模型": "Grok-3-beta",
        "Grok-3-推理模型": "Grok-3-beta",
    }

    @classmethod
    async def create_authed(
        cls, model: str, messages: Messages, auth_result: AuthResult, proxy: str = None, timeout: int = 180,
        action: str = "next", conversation = None, media: MediaListType = None,
        return_conversation: bool = False, web_search: bool = False, **kwargs
    ) -> AsyncResult:
        try:
            # Log the model being used
            debug.log(f"{cls.__name__}: Using model '{model}'")

            # Special handling for Chinese model names
            if model == "Grok-3-推理模型" or model == "grok-3-推理模型":
                debug.log(f"{cls.__name__}: Converting Chinese model name '{model}' to 'Grok-3-beta'")
                model = "Grok-3-beta"
            elif model == "4o":
                debug.log(f"{cls.__name__}: Converting shorthand model name '4o' to 'gpt-4o'")
                model = "gpt-4o"

            # Call the parent class's create_authed method and properly yield the results
            parent_generator = super().create_authed(
                model=model, messages=messages, auth_result=auth_result, proxy=proxy, timeout=timeout,
                action=action, conversation=conversation, media=media,
                return_conversation=return_conversation, web_search=web_search, **kwargs
            )

            # Properly yield each item from the parent generator
            async for item in parent_generator:
                # Skip 'None' strings
                if item is not None and str(item) != "None":
                    yield item

        except Exception as e:
            # Enhanced error logging
            debug.error(f"{cls.__name__}: Error in create_authed: {type(e).__name__}: {e}")
            # Re-raise the exception
            raise

    # Use the parent class's custom iter_messages_line method
    iter_messages_line = Chatshare.iter_messages_line