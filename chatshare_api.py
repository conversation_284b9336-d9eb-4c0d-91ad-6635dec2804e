#!/usr/bin/env python3
from __future__ import annotations

import os
import sys
import re
import argparse
import uvicorn
import base64
import json
import logging
import time
import asyncio
from urllib.parse import urlparse, urlunparse, parse_qs, urlencode
from fastapi import FastAPI, Response, Request, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse, RedirectResponse, HTMLResponse, JSONResponse
from fastapi.security import API<PERSON>eyHeader, HTTPBearer
from typing import List, Union, Optional, Dict, Any

import g4f
import g4f.Provider
from g4f.Provider.needs_auth import Chatshare, ChatshareAccount
from g4f.client import AsyncClient
from g4f.api import ErrorResponse, format_exception
from g4f.api.stubs import ChatCompletionsConfig, ModelResponseModel
from g4f.client.helper import filter_none

# Try to import node_manager
try:
    from node_manager import node_manager
    USE_NODE_MANAGER = True
except ImportError:
    USE_NODE_MANAGER = False
    print("Warning: node_manager not found, dynamic node selection will be disabled")
from starlette.status import (
    HTTP_200_OK,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_404_NOT_FOUND,
    HTTP_401_UNAUTHORIZED,
    HTTP_403_FORBIDDEN,
    HTTP_500_INTERNAL_SERVER_ERROR,
)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Set logger level based on environment variable
if os.environ.get('G4F_DEBUG', 'false').lower() == 'true':
    logger.setLevel(logging.DEBUG)
    logger.info("ChatShare API Debug logging enabled.")

# --- URL Masking Helpers ---

# Regex to identify ChatShare related URLs - broader pattern to match more domains
# This pattern matches any URL containing chat, gpt, share, or shareuc in the domain
CHATSHARE_URL_PATTERN = re.compile(r'https?://[^\\s\'"]*(chat|gpt|share|shareuc)[^\\s\'"]*')

def is_chatshare_url(url: str) -> bool:
    """Check if the URL matches the ChatShare pattern."""
    return bool(CHATSHARE_URL_PATTERN.match(url))

def encode_base64_url_safe(url: str) -> str:
    """Encodes a URL using URL-safe base64, removing padding."""
    return base64.urlsafe_b64encode(url.encode('utf-8')).rstrip(b'=').decode('utf-8')

def mask_url(url: str, proxy_domain: str) -> str:
    """Masks a single ChatShare URL, preserving the query string."""
    if not is_chatshare_url(url):
        return url

    logger.debug(f"Attempting to mask URL: {url}")

    # Ensure proxy_domain starts with http:// or https://
    # Default to HTTPS if no protocol is specified
    if not proxy_domain.startswith(('http://', 'https://')):
        proxy_domain = f"https://{proxy_domain}"

    # Parse the original URL
    parsed_original = urlparse(url)
    original_netloc = parsed_original.netloc
    original_path = parsed_original.path
    original_query = parsed_original.query

    # Extract subdomain and domain parts
    domain_parts = original_netloc.split('.')

    # Determine subdomain and domain ending
    if len(domain_parts) >= 3:
        # Has subdomain: subdomain.chatshare.biz
        subdomain = domain_parts[0]
        domain_ending = domain_parts[-1]
    else:
        # No subdomain: chatshare.biz
        subdomain = "N"  # N indicates no subdomain
        domain_ending = domain_parts[-1]

    # Construct the new path format
    new_path = f"/p/{subdomain}/{domain_ending}/{original_path.lstrip('/')}"

    # Construct masked URL, appending original query string if it exists
    masked_url = f"{proxy_domain}{new_path}"
    if original_query:
        masked_url += f"?{original_query}"

    # For backward compatibility, also keep the encoded version available
    # This can be removed once all clients are updated
    original_base = urlunparse((parsed_original.scheme, parsed_original.netloc, parsed_original.path, '', '', ''))
    encoded_base = encode_base64_url_safe(original_base)
    encoded_url = f"{proxy_domain}/proxy/{encoded_base}"

    logger.debug(f"Masking URL: {url} -> {masked_url} (legacy: {encoded_url})")
    return masked_url

def mask_urls_in_text(text: str, proxy_domain: str) -> str:
    """Finds and masks all ChatShare URLs within a text string."""
    if not isinstance(text, str):
        return text



    def replace_match(match):
        return mask_url(match.group(0), proxy_domain)

    return CHATSHARE_URL_PATTERN.sub(replace_match, text)

def mask_urls_in_json(data: Union[dict, list, str], proxy_domain: str) -> Union[dict, list, str]:
    """Recursively masks ChatShare URLs in JSON-like structures (dicts, lists, strings)."""
    if isinstance(data, dict):
        return {k: mask_urls_in_json(v, proxy_domain) for k, v in data.items()}
    elif isinstance(data, list):
        return [mask_urls_in_json(item, proxy_domain) for item in data]
    elif isinstance(data, str):
        return mask_urls_in_text(data, proxy_domain)
    else:
        return data

# --- End URL Masking Helpers ---

# Initialize Chatshare models
Chatshare.get_models()

class ChatShareAPI:
    def __init__(self, app: FastAPI) -> None:
        self.app = app
        self.client = AsyncClient()
        self.security = HTTPBearer(auto_error=False)

    def register_routes(self):
        @self.app.get("/")
        async def read_root():
            return RedirectResponse("/v1", 302)

        @self.app.get("/v1")
        async def read_root_v1():
            return HTMLResponse('ChatShare API: Go to '
                                '<a href="/v1/models">models</a>, '
                                '<a href="/v1/chat/completions">chat/completions</a>, or '
                                '<a href="/v1/node/current">node/current</a>')

        # Node management endpoints
        @self.app.get("/v1/node/current")
        async def get_current_node():
            """Get information about the current node."""
            if not USE_NODE_MANAGER:
                return {"error": "Node manager not available"}

            return node_manager.get_current_node()

        @self.app.post("/v1/node/speedtest")
        async def trigger_speedtest(background_tasks: BackgroundTasks):
            """Manually trigger a speedtest."""
            if not USE_NODE_MANAGER:
                return {"error": "Node manager not available"}

            # Run speedtest in background to avoid blocking
            background_tasks.add_task(node_manager.run_speedtest)

            return {
                "message": "Speedtest started in background",
                "current_node": node_manager.current_node,
                "check_status_endpoint": "/v1/node/current"
            }

        @self.app.get("/v1/speedtest")
        async def speedtest_endpoint():
            """Simple endpoint for speedtesting."""
            return {"status": "ok", "timestamp": time.time()}

        @self.app.get("/v1/models", responses={
            HTTP_200_OK: {"model": List[ModelResponseModel]},
        })
        async def models():
            # Get only Chatshare models
            chatshare_models = Chatshare.get_models()

            # Include only regular models (not prefixed models) as requested
            all_models = []

            # Add regular models
            all_models.extend([{
                "id": model,
                "object": "model",
                "created": 0,
                "owned_by": "ChatShare",
                "image": False,
                "provider": False,
            } for model in chatshare_models])

            # Add the providers
            all_models.append({
                "id": "Chatshare",
                "object": "model",
                "created": 0,
                "owned_by": Chatshare.label,
                "image": False,
                "provider": True,
            })

            all_models.append({
                "id": "ChatshareAccount",
                "object": "model",
                "created": 0,
                "owned_by": ChatshareAccount.label,
                "image": False,
                "provider": True,
            })

            return {
                "object": "list",
                "data": all_models
            }

        @self.app.post("/v1/chat/completions", responses={
            HTTP_200_OK: {"model": dict},
            HTTP_401_UNAUTHORIZED: {"model": dict},
            HTTP_404_NOT_FOUND: {"model": dict},
            HTTP_422_UNPROCESSABLE_ENTITY: {"model": dict},
            HTTP_500_INTERNAL_SERVER_ERROR: {"model": dict},
        })
        async def chat_completions(
            config: ChatCompletionsConfig,
        ):
            # URL Masking Integration
            proxy_domain = os.environ.get("PROXY_DOMAIN")
            masking_enabled = bool(proxy_domain)
            if masking_enabled:
                logger.info(f"URL Masking enabled via PROXY_DOMAIN: {proxy_domain}")

            try:
                # Set provider to ChatshareRotator to ensure account rotation
                config.provider = "ChatshareRotator"

                # Log the request details for debugging
                logger.info(f"Chat completions request: model={config.model}, provider={config.provider}")

                # Ensure the model is properly formatted
                if config.model == "4o":
                    config.model = "gpt-4o"
                    logger.info(f"Converted model '4o' to 'gpt-4o'")

                # Log the messages for debugging
                if config.messages:
                    logger.info(f"Request messages: {len(config.messages)} messages")

                # Create the completion response
                response = self.client.chat.completions.create(
                    **filter_none(
                        **config.dict(exclude_none=True),
                        **{
                            "conversation_id": None,
                            "return_conversation": config.return_conversation,
                            "conversation": config.conversation
                        }
                    ),
                )

                if not config.stream:
                    # Await the response object
                    response_obj = await response
                    # Check if response_obj is serializable (e.g., Pydantic model)
                    if hasattr(response_obj, 'dict'):
                        response_data = response_obj.dict(exclude_none=True)
                    elif isinstance(response_obj, dict):
                        response_data = response_obj
                    else:
                        # Attempt to handle potential plain text or other types if needed
                        # For now, assume JSON-like structure is primary target
                        logger.warning(f"Non-streaming response type not handled for masking: {type(response_obj)}")
                        return response_obj # Return original if unsure how to process

                    # Process the response data for both URL masking and alt text replacement
                    try:
                        # Replace Chinese "图片" alt text with "Generated Image" in content
                        if isinstance(response_data, dict) and "choices" in response_data and len(response_data["choices"]) > 0:
                            choice = response_data["choices"][0]
                            if "message" in choice and "content" in choice["message"]:
                                content = choice["message"]["content"]
                                # Replace Chinese alt text with English
                                if "![图片]" in content:
                                    content = content.replace("![图片]", "![Generated Image]")
                                    choice["message"]["content"] = content

                        # Apply URL masking if enabled
                        if masking_enabled:
                            logger.debug("Applying URL masking to non-streaming response.")
                            response_data = mask_urls_in_json(response_data, proxy_domain)

                        return JSONResponse(content=response_data)
                    except Exception as process_err:
                        logger.error(f"Error processing non-streaming response: {process_err}", exc_info=True)
                        # Return original object/data if processing fails
                        return response_obj

                async def streaming():
                    try:
                        async for chunk in response:
                            # Skip None chunks and ensure we have json method
                            if chunk is None or str(chunk) == "None" or not hasattr(chunk, 'json'):
                                continue

                            # Get the JSON representation of the chunk
                            chunk_json = chunk.json()

                            # Replace 'None' with empty string in the content if present
                            if '"content":"None"' in chunk_json or '"content": "None"' in chunk_json:
                                chunk_json = chunk_json.replace('"content":"None"', '"content":""').replace('"content": "None"', '"content": ""')

                            # Handle thinking tags in content
                            if '<think>' in chunk_json or '</think>' in chunk_json:
                                # First, normalize all newlines to \n for consistency
                                chunk_json = chunk_json.replace('\r\n', '\n').replace('\r', '\n')

                                # Properly format thinking tags with clear separation
                                chunk_json = chunk_json.replace('<think>', '\\n<think>\\n\\n')
                                chunk_json = chunk_json.replace('</think>', '\\n\\n</think>\\n')

                                # Ensure proper spacing between status messages in thinking sections
                                if 'Status |' in chunk_json:
                                    # First, fix any adjacent status messages without any separator
                                    chunk_json = re.sub(r'(Status \| [0-9.]+%)(Status \|)', r'\1\\n\2', chunk_json)

                                    # Then add proper line breaks between all status messages
                                    chunk_json = re.sub(r'(Status \| [^\\]*?)\\n(Status \|)', r'\1\\n\\n\2', chunk_json)

                                    # Make sure percentage status messages are properly separated
                                    chunk_json = re.sub(r'([0-9.]+%)(Status \|)', r'\1\\n\2', chunk_json)
                            else:
                                # For non-thinking content, just normalize newlines
                                chunk_json = chunk_json.replace('\r\n', '\n').replace('\r', '\n')

                            # Then ensure all literal newlines are properly escaped in JSON
                            # This is crucial for maintaining valid JSON format
                            chunk_json = chunk_json.replace('\n', '\\n')

                            # --- URL Masking and Alt Text Replacement for Streaming ---
                            try:
                                # Parse the JSON string
                                chunk_data = json.loads(chunk_json)

                                # Replace Chinese "图片" alt text with "Generated Image" in content
                                if chunk_data.get("choices") and len(chunk_data["choices"]) > 0:
                                    choice = chunk_data["choices"][0]
                                    if choice.get("delta") and choice["delta"].get("content"):
                                        content = choice["delta"]["content"]
                                        # Replace Chinese alt text with English
                                        if "![图片]" in content:
                                            content = content.replace("![图片]", "![Generated Image]")
                                            choice["delta"]["content"] = content

                                # Apply URL masking if enabled
                                if masking_enabled:
                                    masked_chunk_data = mask_urls_in_json(chunk_data, proxy_domain)
                                    chunk_data = masked_chunk_data

                                # Convert back to JSON string
                                chunk_json = json.dumps(chunk_data, ensure_ascii=False)

                            except json.JSONDecodeError as json_err:
                                logger.error(f"Error decoding JSON chunk for processing: {json_err} - Chunk: {chunk_json[:200]}...")
                                # Continue with original chunk if parsing fails
                            except Exception as process_err:
                                logger.error(f"Error processing chunk: {process_err}", exc_info=True)
                                # Continue with original chunk if processing fails unexpectedly

                            # --- End URL Masking for Streaming ---

                            # Send the formatted JSON chunk (masked or original)
                            yield f"data: {chunk_json}\n\n"
                    except GeneratorExit:
                        pass
                    except Exception as e:
                        yield f'data: {format_exception(e, config)}\n\n'
                    yield "data: [DONE]\n\n"

                return StreamingResponse(streaming(), media_type="text/event-stream")

            except Exception as e:
                return ErrorResponse.from_exception(e, config, HTTP_500_INTERNAL_SERVER_ERROR)

def create_app():
    app = FastAPI()

    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origin_regex=".*",
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    api = ChatShareAPI(app)
    api.register_routes()

    # Add startup and shutdown events for node manager
    @app.on_event("startup")
    async def startup_event():
        if USE_NODE_MANAGER:
            # Start background task for node selection
            asyncio.create_task(node_manager.start_background_task())
            logger.info("Node manager background task started")

    @app.on_event("shutdown")
    def shutdown_event():
        if USE_NODE_MANAGER:
            # Stop background task
            node_manager.stop_background_task()
            logger.info("Node manager background task stopped")

    return app

def run_chatshare_api(
    host: str = '0.0.0.0',
    port: int = 8081,
    debug: bool = False,
    **kwargs
) -> None:
    print(f'Starting ChatShare API server on port {port}... [g4f v-{g4f.version.utils.current_version}]' +
          (" (debug)" if debug else ""))

    uvicorn.run(
        "chatshare_api:create_app",
        host=host,
        port=int(port),
        factory=True,
        use_colors=debug,
        **filter_none(**kwargs)
    )

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Run the ChatShare API")
    parser.add_argument("--port", "-p", default=8081, help="Change the port of the server.")
    parser.add_argument("--debug", "-d", action="store_true", help="Enable verbose logging.")

    args = parser.parse_args()

    if args.debug:
        g4f.debug.logging = True

    run_chatshare_api(port=args.port, debug=args.debug)
