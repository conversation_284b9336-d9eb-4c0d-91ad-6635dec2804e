#!/usr/bin/env python3
"""
File manager for the account rotation system.
Handles file operations for auth files and state persistence.
"""
import os
import json
import time
import random
import logging
import shutil  # Added for disk space checks
import fcntl  # Added for file locking
import errno  # Added for error handling
from typing import Dict, Any, Optional

from .config import AUTH_FILE_PATH, STATE_FILE_PATH, AUTH_FILES_DIR, HAR_COOKIES_DIR

# Configure logging
# Use DEBUG level for more detailed logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FileManager:
    """Handles file operations for the account rotation system."""

    def __init__(self):
        """Initialize the file manager."""
        # Ensure directories exist
        self.ensure_directories()

        # Initialize state
        self.state = self.load_state()

        # Track last save time for batched saves
        self.last_save_time = time.time()

        # Ensure auth file exists with at least empty content
        self.ensure_auth_file_exists()

    def ensure_directories(self):
        """Ensure required directories exist."""
        os.makedirs(HAR_COOKIES_DIR, exist_ok=True)
        os.makedirs(AUTH_FILES_DIR, exist_ok=True)
        logger.info(f"Ensured directories exist: {HAR_COOKIES_DIR}, {AUTH_FILES_DIR}")

    def ensure_auth_file_exists(self):
        """Ensure auth file exists with at least empty content."""
        if not os.path.exists(AUTH_FILE_PATH) or os.path.getsize(AUTH_FILE_PATH) == 0:
            logger.info(f"Auth file does not exist or is empty, creating default auth file: {AUTH_FILE_PATH}")
            try:
                # Create a default auth file with empty cookies and headers
                default_content = {
                    "cookies": {},
                    "headers": {}
                }
                content_json = json.dumps(default_content, indent=2)

                # Ensure directory exists
                os.makedirs(os.path.dirname(AUTH_FILE_PATH), exist_ok=True)

                # Try direct write first
                with open(AUTH_FILE_PATH, 'w') as f:
                    f.write(content_json)
                    f.flush()
                    os.fsync(f.fileno())

                # Set permissions
                try:
                    # Set very permissive permissions (rw-rw-rw-)
                    os.chmod(AUTH_FILE_PATH, 0o666)
                    logger.info(f"Set permissions on default auth file to 0o666")

                    # Also set permissions on the directory to ensure it's accessible
                    dir_path = os.path.dirname(AUTH_FILE_PATH)
                    os.chmod(dir_path, 0o777)  # rwxrwxrwx
                    logger.info(f"Set permissions on directory {dir_path} to 0o777")

                    # Log file stats for debugging
                    import stat
                    st = os.stat(AUTH_FILE_PATH)
                    logger.info(f"Auth file permissions: {stat.filemode(st.st_mode)}")
                    logger.info(f"Auth file owner: {st.st_uid}, group: {st.st_gid}")

                    # Log directory stats for debugging
                    st_dir = os.stat(dir_path)
                    logger.info(f"Directory {dir_path} permissions: {stat.filemode(st_dir.st_mode)}")
                    logger.info(f"Directory {dir_path} owner: {st_dir.st_uid}, group: {st_dir.st_gid}")
                except Exception as e:
                    logger.warning(f"Could not set permissions on default auth file: {e}")

                logger.info(f"Created default auth file: {AUTH_FILE_PATH}")
            except Exception as e:
                logger.error(f"Error creating default auth file: {e}")
        else:
            logger.debug(f"Auth file already exists: {AUTH_FILE_PATH}")

    def _acquire_lock(self, file_path: str, timeout: int = 10) -> Optional[int]:
        """
        Acquire an exclusive lock on a file.

        Args:
            file_path: Path to the file to lock
            timeout: Maximum time to wait for lock in seconds

        Returns:
            Optional[int]: File descriptor if lock acquired, None otherwise
        """
        # Use a lock file in the same directory as the target file for Docker volume compatibility
        dir_path = os.path.dirname(file_path)
        base_name = os.path.basename(file_path)
        lock_path = os.path.join(dir_path, f".{base_name}.lock")

        start_time = time.time()

        # Ensure the directory exists
        try:
            os.makedirs(dir_path, exist_ok=True)
        except Exception as e:
            logger.warning(f"Error creating directory for lock file: {e}")

        # Set very permissive permissions on the directory
        try:
            os.chmod(dir_path, 0o777)  # rwxrwxrwx
        except Exception as e:
            logger.warning(f"Error setting permissions on directory: {e}")

        while time.time() - start_time < timeout:
            try:
                # Open the lock file with permissive permissions
                fd = os.open(lock_path, os.O_CREAT | os.O_WRONLY, 0o666)

                # Try to acquire an exclusive lock
                fcntl.flock(fd, fcntl.LOCK_EX | fcntl.LOCK_NB)

                # Write PID and hostname to lock file for debugging
                hostname = os.uname().nodename
                pid = os.getpid()
                lock_info = f"{hostname}:{pid}:{int(time.time())}"
                os.write(fd, lock_info.encode())

                # Set permissive permissions on the lock file
                try:
                    os.chmod(lock_path, 0o666)  # rw-rw-rw-
                except Exception as e:
                    logger.warning(f"Error setting permissions on lock file: {e}")

                logger.debug(f"Acquired lock on {file_path} (lock file: {lock_path})")

                # Return file descriptor
                return fd
            except (IOError, OSError) as e:
                # Close file descriptor if we opened it
                if 'fd' in locals():
                    os.close(fd)

                # If lock is held by another process, wait and retry
                if e.errno == errno.EAGAIN or e.errno == errno.EACCES:
                    # Try to read the lock file to see who holds it
                    try:
                        with open(lock_path, 'r') as f:
                            lock_holder = f.read().strip()
                            logger.debug(f"Lock on {file_path} is held by {lock_holder}, waiting...")
                    except Exception:
                        pass

                    time.sleep(0.1)
                    continue
                else:
                    # For other errors, log and continue without lock
                    logger.warning(f"Error acquiring lock for {file_path}: {e}")
                    return None

        # Timeout reached
        logger.warning(f"Timeout reached while trying to acquire lock for {file_path}")
        return None

    def _release_lock(self, fd: int, file_path: str):
        """
        Release a lock on a file.

        Args:
            fd: File descriptor
            file_path: Path to the file that was locked
        """
        if fd is not None:
            try:
                # Release the lock
                fcntl.flock(fd, fcntl.LOCK_UN)

                # Close the file descriptor
                os.close(fd)

                # Remove the lock file
                dir_path = os.path.dirname(file_path)
                base_name = os.path.basename(file_path)
                lock_path = os.path.join(dir_path, f".{base_name}.lock")

                if os.path.exists(lock_path):
                    try:
                        os.unlink(lock_path)
                        logger.debug(f"Released lock on {file_path} (removed lock file: {lock_path})")
                    except Exception as e:
                        logger.warning(f"Error removing lock file {lock_path}: {e}")
            except Exception as e:
                logger.warning(f"Error releasing lock for {file_path}: {e}")

    def atomic_write(self, file_path: str, content: str) -> bool:
        """Write content to file atomically with file locking."""
        # Acquire lock
        lock_fd = self._acquire_lock(file_path)

        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            # Log the directory permissions
            dir_path = os.path.dirname(file_path)
            try:
                dir_stat = os.stat(dir_path)
                dir_mode = dir_stat.st_mode & 0o777
                dir_owner = dir_stat.st_uid
                dir_group = dir_stat.st_gid
                logger.debug(f"Directory {dir_path} permissions: mode={oct(dir_mode)}, owner={dir_owner}, group={dir_group}")
            except Exception as stat_e:
                logger.warning(f"Could not get directory stats for {dir_path}: {stat_e}")

            # Write to temporary file
            temp_path = f"{file_path}.tmp"
            logger.debug(f"Writing to temporary file: {temp_path}")

            with open(temp_path, 'w') as f:
                f.write(content)
                f.flush()
                os.fsync(f.fileno())

            # Verify the temporary file was written correctly
            if os.path.exists(temp_path):
                temp_size = os.path.getsize(temp_path)
                logger.debug(f"Temporary file size: {temp_size} bytes")
                if temp_size == 0:
                    logger.warning(f"Temporary file {temp_path} is empty")
            else:
                logger.error(f"Temporary file {temp_path} does not exist after write")
                return False

            # If target file exists, try to remove it first to avoid permission issues
            if os.path.exists(file_path):
                try:
                    # Make a backup of the existing file
                    backup_path = f"{file_path}.bak"
                    os.rename(file_path, backup_path)
                    logger.debug(f"Created backup of existing file: {backup_path}")
                except Exception as backup_e:
                    logger.warning(f"Could not create backup of {file_path}: {backup_e}")
                    # Try to remove the file directly
                    try:
                        os.remove(file_path)
                        logger.debug(f"Removed existing file: {file_path}")
                    except Exception as remove_e:
                        logger.error(f"Could not remove existing file {file_path}: {remove_e}")
                        # Try direct write as a last resort
                        try:
                            with open(file_path, 'w') as f:
                                f.write(content)
                                f.flush()
                                os.fsync(f.fileno())
                            logger.info(f"Direct write to {file_path} successful")
                            return True
                        except Exception as direct_e:
                            logger.error(f"Direct write to {file_path} failed: {direct_e}")
                            return False

            # Rename temporary file to target file (atomic operation)
            logger.debug(f"Renaming {temp_path} to {file_path}")
            os.rename(temp_path, file_path)

            # Set permissions to ensure file is readable/writable
            try:
                # Set very permissive permissions (rw-rw-rw-)
                os.chmod(file_path, 0o666)
                logger.debug(f"Set permissions on {file_path} to 0o666")

                # Also set permissions on the directory to ensure it's accessible
                dir_path = os.path.dirname(file_path)
                os.chmod(dir_path, 0o777)  # rwxrwxrwx
                logger.debug(f"Set permissions on directory {dir_path} to 0o777")

                # Log file stats for debugging
                import stat
                st = os.stat(file_path)
                logger.debug(f"File {file_path} permissions: {stat.filemode(st.st_mode)}")
                logger.debug(f"File {file_path} owner: {st.st_uid}, group: {st.st_gid}")

                # Log directory stats for debugging
                st_dir = os.stat(dir_path)
                logger.debug(f"Directory {dir_path} permissions: {stat.filemode(st_dir.st_mode)}")
                logger.debug(f"Directory {dir_path} owner: {st_dir.st_uid}, group: {st_dir.st_gid}")
            except Exception as e:
                logger.warning(f"Could not set permissions on {file_path}: {e}")

            # Final verification
            if os.path.exists(file_path):
                final_size = os.path.getsize(file_path)
                logger.debug(f"Final file size: {final_size} bytes")
                if final_size == 0:
                    logger.warning(f"Final file {file_path} is empty")
            else:
                logger.error(f"Final file {file_path} does not exist after rename")
                return False

            return True
        except Exception as e:
            logger.error(f"Error writing to {file_path}: {e}")
            return False
        finally:
            # Always release the lock
            if 'lock_fd' in locals() and lock_fd is not None:
                self._release_lock(lock_fd, file_path)

    def load_state(self) -> Dict[str, Any]:
        """Load state from state file with recovery mechanism."""
        default_state = {
            "current_account_index": 0,
            "accounts": [],
            "etag": None,
            "last_modified": None,
            "last_rotation_time": time.time(),
            "usage_stats": {},
            "health_stats": {},
            "total_accounts": 0,
            "available_accounts": 0
        }

        # Try to load from primary state file
        primary_state = self._load_state_from_file(STATE_FILE_PATH)
        if primary_state:
            logger.info(f"Successfully loaded state from primary file: {STATE_FILE_PATH}")
            # Create a backup of the successfully loaded state
            self._create_state_backup(primary_state)
            return primary_state

        # Try to load from backup state file
        backup_path = f"{STATE_FILE_PATH}.bak"
        if os.path.exists(backup_path):
            logger.warning(f"Primary state file failed, attempting to recover from backup: {backup_path}")
            backup_state = self._load_state_from_file(backup_path)
            if backup_state:
                logger.info(f"Successfully recovered state from backup file")
                # Restore the backup to the primary file
                self.save_state(backup_state, force=True)
                return backup_state
            else:
                logger.error(f"Backup state file also failed to load")

        # If all else fails, try to find any valid state file in the directory
        dir_path = os.path.dirname(STATE_FILE_PATH)
        if os.path.exists(dir_path):
            logger.warning(f"Attempting to find any valid state file in directory: {dir_path}")
            for filename in os.listdir(dir_path):
                if filename.startswith(os.path.basename(STATE_FILE_PATH)) and filename.endswith('.bak'):
                    recovery_path = os.path.join(dir_path, filename)
                    logger.info(f"Trying to load state from recovery file: {recovery_path}")
                    recovery_state = self._load_state_from_file(recovery_path)
                    if recovery_state:
                        logger.info(f"Successfully recovered state from file: {recovery_path}")
                        # Restore the recovery state to the primary file
                        self.save_state(recovery_state, force=True)
                        return recovery_state

        # If all recovery attempts fail, use default state
        logger.warning(f"All recovery attempts failed, using default state")
        return default_state

    def _load_state_from_file(self, file_path: str) -> Optional[Dict[str, Any]]:
        """Load state from a specific file path with validation."""
        if not os.path.exists(file_path):
            logger.info(f"State file not found: {file_path}")
            return None

        try:
            with open(file_path, 'r') as f:
                state_data = f.read()

                # Check if file is empty
                if not state_data.strip():
                    logger.error(f"State file is empty: {file_path}")
                    return None

                # Try to parse JSON
                state = json.loads(state_data)

                # Validate state structure
                if not isinstance(state, dict):
                    logger.error(f"State file does not contain a valid dictionary: {file_path}")
                    return None

                # Check for required keys
                required_keys = ["accounts", "current_account_index"]
                for key in required_keys:
                    if key not in state:
                        logger.error(f"State file missing required key '{key}': {file_path}")
                        return None

                logger.info(f"Successfully loaded and validated state from: {file_path}")
                return state
        except json.JSONDecodeError as json_err:
            logger.error(f"JSON parsing error in state file {file_path}: {json_err}")
            return None
        except Exception as e:
            logger.error(f"Error loading state from {file_path}: {e}")
            return None

    def _create_state_backup(self, state: Dict[str, Any]) -> bool:
        """Create a backup of the current state."""
        backup_path = f"{STATE_FILE_PATH}.bak"
        try:
            # Convert to JSON and write atomically
            state_json = json.dumps(state, indent=2)
            success = self.atomic_write(backup_path, state_json)

            if success:
                logger.debug(f"Created backup of state at: {backup_path}")
            else:
                logger.warning(f"Failed to create backup of state at: {backup_path}")

            return success
        except Exception as e:
            logger.error(f"Error creating state backup: {e}")
            return False

    def _check_disk_space(self, file_path: str, required_mb: float = 10.0) -> bool:
        """
        Check if there's enough disk space available.

        Args:
            file_path: Path to check disk space for
            required_mb: Required space in MB

        Returns:
            bool: True if enough space, False otherwise
        """
        try:
            # Get directory from file path
            dir_path = os.path.dirname(file_path)
            if not dir_path:
                dir_path = "."

            # Get disk usage
            disk_usage = shutil.disk_usage(dir_path)
            free_mb = disk_usage.free / (1024 * 1024)  # Convert to MB

            # Check if enough space
            if free_mb < required_mb:
                logger.error(f"Not enough disk space to save state. Required: {required_mb}MB, Available: {free_mb:.2f}MB")
                return False

            return True
        except Exception as e:
            logger.warning(f"Error checking disk space: {e}")
            # Continue anyway if we can't check
            return True

    def save_state(self, state: Dict[str, Any], force: bool = False) -> bool:
        """
        Save state to state file with backup creation.

        Args:
            state: The state to save
            force: If True, always save; if False, use probabilistic saving

        Returns:
            bool: True if state was saved, False otherwise
        """
        # Use probabilistic saving to reduce disk I/O
        # Only save if forced or 5% chance or it's been more than 5 minutes
        current_time = time.time()
        time_since_last_save = current_time - self.last_save_time

        # Always save if forced
        if force:
            logger.info(f"Forced save of state file")
        elif random.random() > 0.05 and time_since_last_save < 300:
            logger.debug(f"Skipping save due to probabilistic saving (last save: {time_since_last_save:.1f} seconds ago)")
            return False
        else:
            logger.info(f"Saving state file (last save: {time_since_last_save:.1f} seconds ago)")

        try:
            # Check disk space before saving
            if not self._check_disk_space(STATE_FILE_PATH):
                logger.error(f"Not enough disk space to save state")
                return False

            # Validate state before saving
            if not self._validate_state(state):
                logger.error(f"State validation failed, not saving")
                return False

            # Update state with summary information
            if "accounts" in state:
                state["total_accounts"] = len(state["accounts"])
                state["available_accounts"] = sum(1 for acc in state["accounts"] if acc.get("availability") == "available")

            # Ensure usage_stats and health_stats exist
            if "usage_stats" not in state:
                logger.warning("Adding missing 'usage_stats' key to state")
                state["usage_stats"] = {}
            if "health_stats" not in state:
                logger.warning("Adding missing 'health_stats' key to state")
                state["health_stats"] = {}

            # Create a backup of the current state file if it exists and is valid
            if os.path.exists(STATE_FILE_PATH):
                try:
                    # Only create backup if current file is valid
                    current_state = self._load_state_from_file(STATE_FILE_PATH)
                    if current_state:
                        # Create timestamped backup for important changes
                        if force:
                            timestamp_backup_path = f"{STATE_FILE_PATH}.{int(current_time)}.bak"
                            with open(STATE_FILE_PATH, 'r') as src, open(timestamp_backup_path, 'w') as dst:
                                dst.write(src.read())
                            logger.debug(f"Created timestamped backup at {timestamp_backup_path}")

                        # Always create regular backup
                        backup_path = f"{STATE_FILE_PATH}.bak"
                        with open(STATE_FILE_PATH, 'r') as src, open(backup_path, 'w') as dst:
                            dst.write(src.read())
                        logger.debug(f"Created backup at {backup_path}")
                except Exception as backup_err:
                    logger.warning(f"Failed to create backup before saving state: {backup_err}")

            # Convert to JSON and write atomically
            state_json = json.dumps(state, indent=2)
            success = self.atomic_write(STATE_FILE_PATH, state_json)

            if success:
                self.last_save_time = current_time
                logger.info(f"Successfully saved state to {STATE_FILE_PATH}")

                # Log state summary for debugging
                total_accounts = state.get("total_accounts", 0)
                available_accounts = state.get("available_accounts", 0)
                usage_stats_count = len(state.get("usage_stats", {}))
                health_stats_count = len(state.get("health_stats", {}))
                logger.info(f"State summary: {total_accounts} total accounts, {available_accounts} available, "
                           f"{usage_stats_count} usage stats, {health_stats_count} health stats")

                # Create a backup after successful save
                self._create_state_backup(state)
            else:
                logger.error(f"Failed to save state to {STATE_FILE_PATH}")

                # Log more details about the failure
                try:
                    if os.path.exists(STATE_FILE_PATH):
                        file_size = os.path.getsize(STATE_FILE_PATH)
                        logger.error(f"Existing state file size: {file_size} bytes")

                        # Check permissions
                        import stat
                        st = os.stat(STATE_FILE_PATH)
                        logger.error(f"State file permissions: {stat.filemode(st.st_mode)}")
                        logger.error(f"State file owner: {st.st_uid}, group: {st.st_gid}")
                    else:
                        logger.error(f"State file does not exist: {STATE_FILE_PATH}")
                except Exception as e:
                    logger.error(f"Error checking state file details: {e}")

            return success
        except Exception as e:
            logger.error(f"Error saving state to {STATE_FILE_PATH}: {e}")
            return False

    def _validate_state(self, state: Any) -> bool:
        """Validate state structure before saving."""
        try:
            # Check if state is a dictionary
            if not isinstance(state, dict):
                logger.error(f"State is not a dictionary")
                return False

            # Check for required keys
            required_keys = ["accounts", "current_account_index"]
            for key in required_keys:
                if key not in state:
                    logger.error(f"State missing required key: {key}")
                    return False

            # Validate accounts structure
            if not isinstance(state["accounts"], list):
                logger.error(f"State accounts is not a list")
                return False

            # Validate current_account_index
            if not isinstance(state["current_account_index"], int):
                logger.error(f"State current_account_index is not an integer")
                return False

            # Validate each account has required fields
            for i, account in enumerate(state["accounts"]):
                if not isinstance(account, dict):
                    logger.error(f"Account at index {i} is not a dictionary")
                    return False

                if "id" not in account:
                    logger.error(f"Account at index {i} missing required key: id")
                    return False

                if "availability" not in account:
                    logger.error(f"Account at index {i} missing required key: availability")
                    return False

            return True
        except Exception as e:
            logger.error(f"Error validating state: {e}")
            return False

    def direct_write(self, file_path: str, content: str) -> bool:
        """Write content to file directly (non-atomic) with file locking."""
        # Acquire lock
        lock_fd = self._acquire_lock(file_path)

        try:
            # Create directory if it doesn't exist
            dir_path = os.path.dirname(file_path)
            os.makedirs(dir_path, exist_ok=True)

            # Set directory permissions first to ensure we can write to it
            try:
                os.chmod(dir_path, 0o777)  # rwxrwxrwx
                logger.info(f"Set permissions on directory {dir_path} to 0o777")
            except Exception as dir_perm_e:
                logger.warning(f"Could not set permissions on directory {dir_path}: {dir_perm_e}")

            # Check if file exists and try to make it writable
            if os.path.exists(file_path):
                try:
                    os.chmod(file_path, 0o666)  # rw-rw-rw-
                    logger.info(f"Set permissions on existing file {file_path} to 0o666")
                except Exception as file_perm_e:
                    logger.warning(f"Could not set permissions on existing file {file_path}: {file_perm_e}")

                # If file exists but we can't write to it, try to remove it
                if not os.access(file_path, os.W_OK):
                    try:
                        os.remove(file_path)
                        logger.info(f"Removed existing file {file_path} that was not writable")
                    except Exception as remove_e:
                        logger.error(f"Could not remove existing file {file_path}: {remove_e}")

            # Write directly to the file
            logger.info(f"Direct write to file: {file_path}")
            with open(file_path, 'w') as f:
                f.write(content)
                f.flush()
                os.fsync(f.fileno())

            # Set permissions
            try:
                # Set very permissive permissions (rw-rw-rw-)
                os.chmod(file_path, 0o666)
                logger.info(f"Set permissions on {file_path} to 0o666")

                # Log file stats for debugging
                import stat
                st = os.stat(file_path)
                logger.info(f"File {file_path} permissions: {stat.filemode(st.st_mode)}")
                logger.info(f"File {file_path} owner: {st.st_uid}, group: {st.st_gid}")

                # Log directory stats for debugging
                st_dir = os.stat(dir_path)
                logger.info(f"Directory {dir_path} permissions: {stat.filemode(st_dir.st_mode)}")
                logger.info(f"Directory {dir_path} owner: {st_dir.st_uid}, group: {st_dir.st_gid}")
            except Exception as e:
                logger.warning(f"Could not set permissions on {file_path}: {e}")

            # Verify the file was written correctly
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                logger.info(f"File {file_path} size after write: {file_size} bytes")
                if file_size == 0:
                    logger.error(f"File {file_path} is empty after write")
                    return False
            else:
                logger.error(f"File {file_path} does not exist after write")
                return False

            return True
        except Exception as e:
            logger.error(f"Error in direct write to {file_path}: {e}")
            return False
        finally:
            # Always release the lock
            if 'lock_fd' in locals() and lock_fd is not None:
                self._release_lock(lock_fd, file_path)

    def update_auth_file(self, account: Dict[str, Any]) -> bool:
        """Update auth file with new account data."""
        try:
            # Check disk space before updating
            if not self._check_disk_space(AUTH_FILE_PATH):
                logger.error(f"Not enough disk space to update auth file")
                return False

            # Log the full AUTH_FILE_PATH for debugging
            logger.info(f"AUTH_FILE_PATH is set to: {AUTH_FILE_PATH}")

            # Check if the HAR_COOKIES_DIR exists and is writable
            if os.path.exists(HAR_COOKIES_DIR):
                logger.info(f"HAR_COOKIES_DIR exists: {HAR_COOKIES_DIR}")
                if os.access(HAR_COOKIES_DIR, os.W_OK):
                    logger.info(f"HAR_COOKIES_DIR is writable")
                else:
                    logger.error(f"HAR_COOKIES_DIR is not writable")
            else:
                logger.error(f"HAR_COOKIES_DIR does not exist: {HAR_COOKIES_DIR}")
                # Try to create it
                try:
                    os.makedirs(HAR_COOKIES_DIR, exist_ok=True)
                    logger.info(f"Created HAR_COOKIES_DIR: {HAR_COOKIES_DIR}")
                except Exception as mkdir_e:
                    logger.error(f"Failed to create HAR_COOKIES_DIR: {mkdir_e}")

            # Extract HAR data from account
            har_data = account.get("har")
            if not har_data:
                logger.error(f"Account {account.get('id')} has no HAR data")
                return False

            # Ensure the HAR data has the correct structure for Chatshare
            # The auth file must have cookies and headers keys
            if not isinstance(har_data, dict):
                logger.error(f"HAR data is not a dictionary: {type(har_data)}")
                har_data = {"cookies": {}, "headers": {}}

            # Ensure cookies and headers exist
            if "cookies" not in har_data:
                logger.warning("Adding missing 'cookies' key to HAR data")
                har_data["cookies"] = {}
            if "headers" not in har_data:
                logger.warning("Adding missing 'headers' key to HAR data")
                har_data["headers"] = {}

            # Ensure headers has required fields for Chatshare API
            headers = har_data.get("headers", {})
            if "user-agent" not in headers and "User-Agent" not in headers:
                logger.warning("Adding default User-Agent to headers")
                headers["user-agent"] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"

            # Log the HAR data structure (without sensitive values)
            har_keys = list(har_data.keys())
            cookies_keys = list(har_data.get("cookies", {}).keys())
            headers_keys = list(har_data.get("headers", {}).keys())
            logger.info(f"HAR data structure: keys={har_keys}, cookies_keys={cookies_keys}, headers_keys={headers_keys}")

            # Convert to JSON and write atomically
            har_json = json.dumps(har_data, indent=2)

            # Log the first 100 characters of the JSON (for debugging)
            logger.info(f"Writing to auth file: {AUTH_FILE_PATH}, content preview: {har_json[:100]}...")

            # Ensure the directory exists
            os.makedirs(os.path.dirname(AUTH_FILE_PATH), exist_ok=True)
            logger.info(f"Ensured directory exists: {os.path.dirname(AUTH_FILE_PATH)}")

            # Check if the file is writable
            if os.path.exists(AUTH_FILE_PATH):
                if not os.access(AUTH_FILE_PATH, os.W_OK):
                    logger.error(f"Auth file {AUTH_FILE_PATH} exists but is not writable")
                    # Try to fix permissions
                    try:
                        os.chmod(AUTH_FILE_PATH, 0o666)  # rw-rw-rw-
                        logger.info(f"Changed permissions on {AUTH_FILE_PATH} to make it writable")
                    except Exception as perm_e:
                        logger.error(f"Failed to change permissions on {AUTH_FILE_PATH}: {perm_e}")

            # Try atomic write first
            success = self.atomic_write(AUTH_FILE_PATH, har_json)

            # If atomic write fails, try direct write
            if not success:
                logger.warning(f"Atomic write failed, trying direct write")
                success = self.direct_write(AUTH_FILE_PATH, har_json)

            if success:
                logger.info(f"Updated auth file with account {account.get('id')}")

                # Verify the file was written correctly
                try:
                    if os.path.exists(AUTH_FILE_PATH):
                        file_size = os.path.getsize(AUTH_FILE_PATH)
                        logger.info(f"Auth file size after update: {file_size} bytes")

                        # Get file permissions
                        try:
                            import stat
                            st = os.stat(AUTH_FILE_PATH)
                            logger.info(f"Auth file permissions: {stat.filemode(st.st_mode)}")
                            logger.info(f"Auth file owner: {st.st_uid}, group: {st.st_gid}")
                        except Exception as stat_e:
                            logger.error(f"Error getting auth file stats: {stat_e}")

                        # Read back the file to verify content
                        with open(AUTH_FILE_PATH, 'r') as f:
                            content = f.read()
                            if content:
                                logger.info(f"Auth file content verification: first 100 chars: {content[:100]}...")
                            else:
                                logger.error(f"Auth file exists but is empty after update")
                                # If file is empty, try direct write again
                                logger.info(f"Auth file is empty, trying direct write again")
                                success = self.direct_write(AUTH_FILE_PATH, har_json)
                    else:
                        logger.error(f"Auth file does not exist after update")
                        # If file doesn't exist, try direct write again
                        logger.info(f"Auth file doesn't exist, trying direct write again")
                        success = self.direct_write(AUTH_FILE_PATH, har_json)
                except Exception as verify_e:
                    logger.error(f"Error verifying auth file after update: {verify_e}")
                    # Print the full exception traceback for debugging
                    import traceback
                    logger.error(f"Traceback: {traceback.format_exc()}")
            else:
                logger.error(f"Failed to update auth file with account {account.get('id')}")

            return success
        except Exception as e:
            logger.error(f"Error updating auth file: {e}")
            return False

    def get_auth_file_content(self) -> Optional[Dict[str, Any]]:
        """Get the current content of the auth file."""
        if not os.path.exists(AUTH_FILE_PATH):
            return None

        try:
            with open(AUTH_FILE_PATH, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error reading auth file: {e}")
            return None
