#!/usr/bin/env python3
"""
Account manager for the account rotation system.
Handles account selection, rotation, and health tracking.
Optimized for resource efficiency with hundreds of accounts.
"""
import time
import random
import logging
import asyncio
import json
from typing import Dict, Any, List, Tuple, Optional
from datetime import datetime, timedelta, timezone
from collections import deque

# Check if aiohttp is installed
try:
    import aiohttp
    AIOHTTP_AVAILABLE = True
except ImportError:
    AIOHTTP_AVAILABLE = False
    print("aiohttp is not installed in account_manager.py.")
    print("Please install it using: apt-get install python3-aiohttp")
    print("Remote account fetching will be disabled.")

from .config import REMOTE_ACCOUNTS_URL, CONFIG, SAMPLE_ACCOUNTS, STATE_FILE_PATH
from .file_manager import FileManager

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AccountManager:
    """Manages accounts for the account rotation system with optimized data structures."""

    def __init__(self):
        """Initialize the account manager with optimized data structures."""
        # Initialize file manager
        self.file_manager = FileManager()

        # Initialize state
        self.state = self.file_manager.state

        # Initialize optimized data structures for O(1) lookups
        self.accounts_by_id = {}  # Dict of account_id -> account
        self.available_accounts = set()  # Set of available account IDs
        self.rate_limited_accounts = {}  # Dict of account_id -> expiry_timestamp

        # Initialize fine-grained locks for concurrent access
        self.lock = asyncio.Lock()  # Global lock for major state changes
        self.rate_limit_lock = asyncio.Lock()  # Lock for rate limit operations
        self.auth_file_lock = asyncio.Lock()  # Lock for auth file updates

        # Initialize request tracking with fixed-size buffers
        self.request_times = {}  # Dict of account_id -> deque of request timestamps

        # Initialize health tracking
        self.connection_failures = {}  # Dict of account_id -> count of consecutive failures
        self.last_failure_time = {}  # Dict of account_id -> timestamp of last failure

        # Batched state persistence
        self.pending_changes = set()  # Set of account_ids with pending changes
        self.batch_threshold = 5  # Number of changes before triggering a save
        self.last_save_time = time.time()

        # Initialize the optimized data structures from state
        self._initialize_optimized_structures()

    def _initialize_optimized_structures(self):
        """Initialize optimized data structures from state."""
        # Clear existing data structures
        self.accounts_by_id.clear()
        self.available_accounts.clear()
        self.rate_limited_accounts.clear()

        # Populate accounts_by_id and available_accounts
        for account in self.state.get("accounts", []):
            account_id = account.get("id")
            if account_id:
                # Add to accounts_by_id
                self.accounts_by_id[account_id] = account

                # Add to available_accounts if available
                if account.get("availability") == "available":
                    self.available_accounts.add(account_id)

                    # Check if rate limited
                    if self._calculate_rate_limits(account_id)[0]:
                        # Add to rate_limited_accounts with expiry time
                        self.rate_limited_accounts[account_id] = self._calculate_rate_limits(account_id)[1]
                        # Remove from available_accounts if rate limited
                        self.available_accounts.discard(account_id)

        logger.debug(f"Initialized optimized structures: {len(self.accounts_by_id)} accounts, "
                    f"{len(self.available_accounts)} available, {len(self.rate_limited_accounts)} rate limited")

    def _calculate_rate_limits(self, account_id: str) -> Tuple[bool, float]:
        """
        Calculate if an account is rate limited and when it will expire.
        Optimized for efficiency with caching of intermediate results.

        Args:
            account_id: The ID of the account

        Returns:
            Tuple[bool, float]: (is_limited, expiry_timestamp)
        """
        # Check if we have a cached result that's still valid
        current_time = time.time()

        # Initialize result variables
        is_limited = False
        expiry_timestamp = 0.0

        # Get usage stats once to avoid repeated dictionary lookups
        usage_stats = self.state.get("usage_stats", {}).get(account_id, {})

        # Check requests per minute limit (most common limit hit)
        request_times = self.request_times.get(account_id)
        if request_times and len(request_times) >= CONFIG["rate_limits"]["requests_per_minute_limit"]:
            # Check if oldest request is within the last minute
            oldest_request = request_times[0]
            time_since_oldest = current_time - oldest_request
            if time_since_oldest < 60:
                # Calculate when the rate limit will expire
                expiry_timestamp = max(expiry_timestamp, oldest_request + 60)
                is_limited = True

                # If this limit is hit, we can often return early without checking others
                if not CONFIG.get("check_all_limits_always", False):
                    return is_limited, expiry_timestamp

        # Check minimum delay between requests (second most common)
        last_request_time = usage_stats.get("last_request_time")
        if last_request_time:
            time_since_last = current_time - last_request_time
            min_delay = CONFIG["rate_limits"]["min_delay_seconds"]
            if time_since_last < min_delay:
                # Calculate when the minimum delay will expire
                min_delay_expiry = last_request_time + min_delay
                expiry_timestamp = max(expiry_timestamp, min_delay_expiry)
                is_limited = True

                # If this limit is hit, we can often return early without checking others
                if not CONFIG.get("check_all_limits_always", False):
                    return is_limited, expiry_timestamp

        # Check daily request limit (least common to hit)
        daily_requests = usage_stats.get("daily_requests", 0)
        if daily_requests >= CONFIG["rate_limits"]["daily_request_limit"]:
            # Calculate when the daily limit will reset (midnight UTC)
            # This is an expensive calculation, so only do it if needed
            current_day = datetime.now(timezone.utc).date()
            next_day = current_day + timedelta(days=1)
            midnight_utc = datetime(next_day.year, next_day.month, next_day.day,
                                   tzinfo=timezone.utc).timestamp()
            expiry_timestamp = max(expiry_timestamp, midnight_utc)
            is_limited = True

        return is_limited, expiry_timestamp

    def _schedule_state_save(self):
        """Schedule a state save based on batching criteria."""
        # Check if we should save state
        current_time = time.time()
        time_since_last_save = current_time - self.last_save_time

        # Save if enough changes or enough time has passed
        if len(self.pending_changes) >= self.batch_threshold or time_since_last_save >= 300:
            # Force save to ensure state is updated
            save_success = self.file_manager.save_state(self.state, force=True)
            if save_success:
                logger.info(f"Successfully saved state with {len(self.pending_changes)} pending changes after {time_since_last_save:.1f} seconds")
                self.pending_changes.clear()
                self.last_save_time = current_time
            else:
                logger.error(f"Failed to save state with {len(self.pending_changes)} pending changes")
                # Try again with direct write as a fallback
                try:
                    state_json = json.dumps(self.state, indent=2)
                    direct_success = self.file_manager.direct_write(STATE_FILE_PATH, state_json)
                    if direct_success:
                        logger.info("Successfully saved state using direct write fallback")
                        self.pending_changes.clear()
                        self.last_save_time = current_time
                    else:
                        logger.error("Failed to save state using direct write fallback")
                except Exception as e:
                    logger.error(f"Error in direct write fallback: {e}")

    def get_current_account(self) -> Optional[Dict[str, Any]]:
        """
        Get the currently active account.

        Returns:
            Dict: The current account, or None if no accounts
        """
        if not self.state.get("accounts"):
            return None

        current_index = self.state.get("current_account_index", 0)
        accounts = self.state.get("accounts", [])

        if not accounts:
            return None

        # Ensure index is valid
        if current_index >= len(accounts):
            current_index = 0
            self.state["current_account_index"] = current_index

        # Get the current account
        current_account = accounts[current_index]

        # Check if the current account is rate limited
        account_id = current_account.get("id")
        if account_id and self.is_rate_limited(account_id):
            logger.info(f"Current account {account_id} is rate limited, will rotate on next request")

        return current_account

    def get_available_accounts(self) -> List[Dict[str, Any]]:
        """
        Get all available accounts that are not rate limited.

        Returns:
            List[Dict]: List of available accounts not at rate limits
        """
        # Use the optimized available_accounts set for O(1) lookups
        # Filter out rate limited accounts
        available_not_limited = []

        # Clean up expired rate limits first
        self._cleanup_expired_rate_limits()

        # Get accounts that are available and not rate limited
        for account_id in self.available_accounts:
            if account_id not in self.rate_limited_accounts:
                account = self.accounts_by_id.get(account_id)
                if account:
                    available_not_limited.append(account)

        return available_not_limited

    def _cleanup_expired_rate_limits(self):
        """Remove expired rate limits from rate_limited_accounts."""
        current_time = time.time()
        expired = []

        # Find expired rate limits
        for account_id, expiry_time in self.rate_limited_accounts.items():
            if current_time >= expiry_time:
                expired.append(account_id)

        # Remove expired rate limits
        for account_id in expired:
            del self.rate_limited_accounts[account_id]
            # Add back to available accounts if the account is still available
            if account_id in self.accounts_by_id and self.accounts_by_id[account_id].get("availability") == "available":
                self.available_accounts.add(account_id)

        if expired:
            logger.debug(f"Cleaned up {len(expired)} expired rate limits")

    def _cleanup_inactive_accounts(self):
        """Remove request tracking for accounts that haven't been used recently."""
        current_time = time.time()
        inactive_threshold = CONFIG.get("inactive_account_threshold", 3600)  # Default 1 hour

        inactive_accounts = []
        for account_id, timestamps in self.request_times.items():
            if not timestamps:
                inactive_accounts.append(account_id)
            elif current_time - max(timestamps) > inactive_threshold:
                inactive_accounts.append(account_id)

        # Remove request tracking for inactive accounts
        for account_id in inactive_accounts:
            del self.request_times[account_id]

        if inactive_accounts:
            logger.debug(f"Cleaned up request tracking for {len(inactive_accounts)} inactive accounts")

        # Enforce maximum size limits on collections
        self._enforce_collection_limits()

    def _enforce_collection_limits(self):
        """Enforce maximum size limits on collections to prevent memory leaks."""
        # Limit the size of request_times
        max_request_times = CONFIG.get("max_request_times", 1000)
        if len(self.request_times) > max_request_times:
            # Remove oldest entries (least recently used)
            excess = len(self.request_times) - max_request_times
            accounts_to_remove = sorted(
                self.request_times.keys(),
                key=lambda acc_id: max(self.request_times[acc_id]) if self.request_times[acc_id] else 0,
                reverse=False  # Oldest first
            )[:excess]

            for account_id in accounts_to_remove:
                del self.request_times[account_id]

            logger.warning(f"Enforced maximum size limit on request_times, removed {excess} oldest entries")

        # Limit the size of rate_limited_accounts
        max_rate_limited = CONFIG.get("max_rate_limited_accounts", 1000)
        if len(self.rate_limited_accounts) > max_rate_limited:
            # Remove entries that will expire soonest
            excess = len(self.rate_limited_accounts) - max_rate_limited
            accounts_to_remove = sorted(
                self.rate_limited_accounts.items(),
                key=lambda item: item[1]  # Sort by expiry time
            )[:excess]

            for account_id, _ in accounts_to_remove:
                del self.rate_limited_accounts[account_id]

            logger.warning(f"Enforced maximum size limit on rate_limited_accounts, removed {excess} entries")

        # Limit the size of pending_changes
        max_pending_changes = CONFIG.get("max_pending_changes", 1000)
        if len(self.pending_changes) > max_pending_changes:
            # Force a state save and clear pending changes
            logger.warning(f"Too many pending changes ({len(self.pending_changes)}), forcing state save")
            self.file_manager.save_state(self.state, force=True)
            self.pending_changes.clear()
            self.last_save_time = time.time()

    def cleanup(self):
        """Run all cleanup operations with robust error handling."""
        try:
            # Clean up expired rate limits
            try:
                self._cleanup_expired_rate_limits()
            except Exception as e:
                logger.error(f"Error cleaning up expired rate limits: {e}")

            # Clean up inactive accounts
            try:
                self._cleanup_inactive_accounts()
            except Exception as e:
                logger.error(f"Error cleaning up inactive accounts: {e}")

            # Clean up pending changes if too many
            try:
                if len(self.pending_changes) > CONFIG.get("batch_threshold", 5) * 10:
                    logger.warning(f"Too many pending changes ({len(self.pending_changes)}), forcing state save")
                    save_success = self.file_manager.save_state(self.state, force=True)
                    if save_success:
                        self.pending_changes.clear()
                        self.last_save_time = time.time()
                    else:
                        logger.error("Failed to save state during cleanup")
            except Exception as e:
                logger.error(f"Error cleaning up pending changes: {e}")
        except Exception as e:
            logger.error(f"Unhandled error in cleanup method: {e}")
            # Continue execution despite errors

    async def fetch_remote_accounts(self) -> Tuple[bool, str]:
        """
        Fetch accounts from remote URL.

        Returns:
            Tuple[bool, str]: (success, message)
        """
        # Check if aiohttp is available
        if not AIOHTTP_AVAILABLE:
            logger.error("Cannot fetch remote accounts: aiohttp is not installed")
            return False, "Cannot fetch remote accounts: aiohttp is not installed"

        try:
            # Get current ETag and Last-Modified
            etag = self.state.get("etag")
            last_modified = self.state.get("last_modified")

            # Prepare headers for conditional request
            headers = {}
            if etag:
                headers["If-None-Match"] = etag
            if last_modified:
                headers["If-Modified-Since"] = last_modified

            # Log the remote accounts URL
            logger.info(f"Fetching accounts from URL: {REMOTE_ACCOUNTS_URL}")

            # Fetch accounts from remote URL
            async with aiohttp.ClientSession() as session:
                logger.debug(f"Created aiohttp ClientSession")
                async with session.get(REMOTE_ACCOUNTS_URL, headers=headers) as response:
                    logger.debug(f"Got response with status: {response.status}, content-type: {response.headers.get('content-type')}")
                    # Check if content has changed
                    if response.status == 304:  # Not Modified
                        logger.debug("Remote accounts not modified")
                        return True, "Remote accounts not modified"

                    # Check for successful response
                    if response.status != 200:
                        logger.error(f"Failed to fetch remote accounts: {response.status}")
                        return False, f"Failed to fetch remote accounts: {response.status}"

                    # Get new ETag and Last-Modified
                    new_etag = response.headers.get("ETag")
                    new_last_modified = response.headers.get("Last-Modified")

                    # Get accounts from response
                    try:
                        # First try to get the text content
                        content_text = await response.text()
                        logger.debug(f"Response content preview: {content_text[:100]}...")

                        # Try to parse as JSON
                        try:
                            import json
                            accounts = json.loads(content_text)
                            logger.debug("Successfully parsed response as JSON")
                        except json.JSONDecodeError as json_err:
                            logger.error(f"Failed to parse response as JSON: {json_err}")
                            logger.warning("Using sample accounts as fallback")
                            accounts = SAMPLE_ACCOUNTS
                            logger.info(f"Loaded {len(accounts)} sample accounts as fallback")
                    except Exception as e:
                        logger.error(f"Failed to get response content: {e}")
                        logger.warning("Using sample accounts as fallback")
                        accounts = SAMPLE_ACCOUNTS
                        logger.info(f"Loaded {len(accounts)} sample accounts as fallback")

                    # Validate accounts
                    if not isinstance(accounts, list):
                        logger.error("Remote accounts is not a list")
                        logger.warning("Using sample accounts as fallback")
                        accounts = SAMPLE_ACCOUNTS
                        logger.info(f"Loaded {len(accounts)} sample accounts as fallback")

                    # Update state
                    async with self.lock:
                        # Keep track of existing account stats
                        usage_stats = self.state.get("usage_stats", {})
                        health_stats = self.state.get("health_stats", {})

                        # Update accounts
                        self.state["accounts"] = accounts
                        self.state["etag"] = new_etag
                        self.state["last_modified"] = new_last_modified

                        # Restore usage and health stats for existing accounts
                        for account in accounts:
                            account_id = account.get("id")
                            if account_id:
                                # Restore usage stats
                                if account_id in usage_stats:
                                    if "usage_stats" not in self.state:
                                        self.state["usage_stats"] = {}
                                    self.state["usage_stats"][account_id] = usage_stats[account_id]

                                # Restore health stats
                                if account_id in health_stats:
                                    if "health_stats" not in self.state:
                                        self.state["health_stats"] = {}
                                    self.state["health_stats"][account_id] = health_stats[account_id]

                        # Save state
                        self.file_manager.save_state(self.state, force=True)

                        # Immediately rotate to an account if we have accounts
                        if accounts and len(accounts) > 0:
                            # Find the first available account
                            available_account = None
                            for acc in accounts:
                                if acc.get("availability") == "available":
                                    available_account = acc
                                    break

                            if available_account:
                                # Update auth file with the first available account
                                success = self.file_manager.update_auth_file(available_account)
                                if success:
                                    logger.info(f"Immediately updated auth file with account {available_account.get('id')} after fetching")
                                else:
                                    logger.error(f"Failed to immediately update auth file with account {available_account.get('id')} after fetching")

                    logger.info(f"Fetched {len(accounts)} accounts from remote URL")
                    return True, f"Fetched {len(accounts)} accounts from remote URL"

        except Exception as e:
            logger.error(f"Error fetching remote accounts: {e}")
            logger.warning("Using sample accounts as fallback")

            # Update state with sample accounts
            async with self.lock:
                # Keep track of existing account stats
                usage_stats = self.state.get("usage_stats", {})
                health_stats = self.state.get("health_stats", {})

                # Update accounts with sample accounts
                self.state["accounts"] = SAMPLE_ACCOUNTS

                # Restore usage and health stats for existing accounts
                for account in SAMPLE_ACCOUNTS:
                    account_id = account.get("id")
                    if account_id:
                        # Restore usage stats
                        if account_id in usage_stats:
                            if "usage_stats" not in self.state:
                                self.state["usage_stats"] = {}
                            self.state["usage_stats"][account_id] = usage_stats[account_id]

                        # Restore health stats
                        if account_id in health_stats:
                            if "health_stats" not in self.state:
                                self.state["health_stats"] = {}
                            self.state["health_stats"][account_id] = health_stats[account_id]

                # Save state
                self.file_manager.save_state(self.state, force=True)

                # Immediately rotate to an account if we have sample accounts
                if SAMPLE_ACCOUNTS and len(SAMPLE_ACCOUNTS) > 0:
                    # Find the first available account
                    available_account = None
                    for acc in SAMPLE_ACCOUNTS:
                        if acc.get("availability") == "available":
                            available_account = acc
                            break

                    if available_account:
                        # Update auth file with the first available account
                        success = self.file_manager.update_auth_file(available_account)
                        if success:
                            logger.info(f"Immediately updated auth file with sample account {available_account.get('id')} after fallback")
                        else:
                            logger.error(f"Failed to immediately update auth file with sample account {available_account.get('id')} after fallback")

            logger.info(f"Loaded {len(SAMPLE_ACCOUNTS)} sample accounts as fallback")
            return True, f"Loaded {len(SAMPLE_ACCOUNTS)} sample accounts as fallback"

    async def record_request(self, account_id: str):
        """
        Record a request for rate limiting with optimized data structures.

        Args:
            account_id: The ID of the account
        """
        async with self.rate_limit_lock:
            # Initialize request times for this account if not exists
            if account_id not in self.request_times:
                self.request_times[account_id] = deque(maxlen=CONFIG["rate_limits"]["requests_per_minute_limit"])

            # Add current timestamp to request times
            current_time = time.time()
            self.request_times[account_id].append(current_time)

            # Update usage stats
            if "usage_stats" not in self.state:
                self.state["usage_stats"] = {}
            if account_id not in self.state["usage_stats"]:
                self.state["usage_stats"][account_id] = {
                    "daily_requests": 0,
                    "last_request_time": None,
                    "total_requests": 0
                }

            # Update usage stats
            self.state["usage_stats"][account_id]["last_request_time"] = current_time
            self.state["usage_stats"][account_id]["total_requests"] = self.state["usage_stats"][account_id].get("total_requests", 0) + 1

            # Update daily requests
            current_day = datetime.now(timezone.utc).date().isoformat()
            if "current_day" not in self.state["usage_stats"][account_id] or self.state["usage_stats"][account_id]["current_day"] != current_day:
                # Reset daily counter if day changed
                self.state["usage_stats"][account_id]["daily_requests"] = 1
                self.state["usage_stats"][account_id]["current_day"] = current_day
            else:
                # Increment daily counter
                self.state["usage_stats"][account_id]["daily_requests"] = self.state["usage_stats"][account_id].get("daily_requests", 0) + 1

            # Check if this request puts the account at rate limits
            is_limited, expiry = self._calculate_rate_limits(account_id)
            if is_limited:
                # Add to rate-limited accounts with expiry time
                self.rate_limited_accounts[account_id] = expiry
                # Remove from available accounts
                self.available_accounts.discard(account_id)
                logger.info(f"Account {account_id} has reached rate limits, will be available again at {datetime.fromtimestamp(expiry)}")

            # Add to pending changes for batched state persistence
            self.pending_changes.add(account_id)

            # Force save state for usage statistics updates
            # This ensures the state file is always updated with the latest usage stats
            save_success = self.file_manager.save_state(self.state, force=True)
            if save_success:
                logger.info(f"Successfully saved state after recording request for account {account_id}")
                self.pending_changes.clear()
                self.last_save_time = current_time
            else:
                logger.error(f"Failed to save state after recording request for account {account_id}")
                # Try again with direct write as a fallback
                try:
                    state_json = json.dumps(self.state, indent=2)
                    direct_success = self.file_manager.direct_write(STATE_FILE_PATH, state_json)
                    if direct_success:
                        logger.info(f"Successfully saved state using direct write after recording request for account {account_id}")
                        self.pending_changes.clear()
                        self.last_save_time = current_time
                    else:
                        logger.error(f"Failed to save state using direct write after recording request for account {account_id}")
                except Exception as e:
                    logger.error(f"Error in direct write fallback after recording request: {e}")

    async def record_failure(self, account_id: str):
        """
        Record a connection failure with optimized data structures.

        Args:
            account_id: The ID of the account
        """
        async with self.lock:
            # Initialize failure count for this account if not exists
            if account_id not in self.connection_failures:
                self.connection_failures[account_id] = 0

            # Increment failure count
            self.connection_failures[account_id] += 1
            current_time = time.time()
            self.last_failure_time[account_id] = current_time

            # Update health stats
            if "health_stats" not in self.state:
                self.state["health_stats"] = {}
            if account_id not in self.state["health_stats"]:
                self.state["health_stats"][account_id] = {
                    "connection_failures": 0,
                    "last_failure_time": None,
                    "last_success_time": None
                }

            # Update health stats
            self.state["health_stats"][account_id]["connection_failures"] = self.connection_failures[account_id]
            self.state["health_stats"][account_id]["last_failure_time"] = current_time

            # Check if max failures reached
            if self.connection_failures[account_id] >= CONFIG["max_connection_failures"]:
                logger.warning(f"Account {account_id} reached max connection failures, marking as unavailable")
                await self.mark_account_unavailable(account_id)

            # Add to pending changes for batched state persistence
            self.pending_changes.add(account_id)

            # Force save state for failures
            # This ensures the state file is always updated with the latest health stats
            save_success = self.file_manager.save_state(self.state, force=True)
            if save_success:
                logger.info(f"Successfully saved state after recording failure for account {account_id}")
                self.pending_changes.clear()
                self.last_save_time = current_time
            else:
                logger.error(f"Failed to save state after recording failure for account {account_id}")
                # Try again with direct write as a fallback
                try:
                    state_json = json.dumps(self.state, indent=2)
                    direct_success = self.file_manager.direct_write(STATE_FILE_PATH, state_json)
                    if direct_success:
                        logger.info(f"Successfully saved state using direct write after recording failure for account {account_id}")
                        self.pending_changes.clear()
                        self.last_save_time = current_time
                    else:
                        logger.error(f"Failed to save state using direct write after recording failure for account {account_id}")
                except Exception as e:
                    logger.error(f"Error in direct write fallback after recording failure: {e}")

    async def record_success(self, account_id: str):
        """
        Record a successful connection with optimized data structures.

        Args:
            account_id: The ID of the account
        """
        async with self.lock:
            # Reset failure count
            self.connection_failures[account_id] = 0

            # Update health stats
            if "health_stats" not in self.state:
                self.state["health_stats"] = {}
            if account_id not in self.state["health_stats"]:
                self.state["health_stats"][account_id] = {
                    "connection_failures": 0,
                    "last_failure_time": None,
                    "last_success_time": None
                }

            # Update health stats
            current_time = time.time()
            self.state["health_stats"][account_id]["connection_failures"] = 0
            self.state["health_stats"][account_id]["last_success_time"] = current_time

            # Add to pending changes for batched state persistence
            self.pending_changes.add(account_id)

            # Force save state for health statistics updates
            # This ensures the state file is always updated with the latest health stats
            save_success = self.file_manager.save_state(self.state, force=True)
            if save_success:
                logger.info(f"Successfully saved state after recording success for account {account_id}")
                self.pending_changes.clear()
                self.last_save_time = current_time
            else:
                logger.error(f"Failed to save state after recording success for account {account_id}")
                # Try again with direct write as a fallback
                try:
                    state_json = json.dumps(self.state, indent=2)
                    direct_success = self.file_manager.direct_write(STATE_FILE_PATH, state_json)
                    if direct_success:
                        logger.info(f"Successfully saved state using direct write after recording success for account {account_id}")
                        self.pending_changes.clear()
                        self.last_save_time = current_time
                    else:
                        logger.error(f"Failed to save state using direct write after recording success for account {account_id}")
                except Exception as e:
                    logger.error(f"Error in direct write fallback after recording success: {e}")

    async def mark_account_unavailable(self, account_id: str):
        """
        Mark an account as unavailable with optimized data structures.

        Args:
            account_id: The ID of the account
        """
        async with self.lock:
            # Update the account in the optimized data structures
            if account_id in self.accounts_by_id:
                self.accounts_by_id[account_id]["availability"] = "unavailable"
                # Remove from available accounts
                self.available_accounts.discard(account_id)
                logger.info(f"Marked account {account_id} as unavailable")

                # Also update in the state list for persistence
                for account in self.state.get("accounts", []):
                    if account.get("id") == account_id:
                        account["availability"] = "unavailable"
                        break

                # Add to pending changes for batched state persistence
                self.pending_changes.add(account_id)

                # Force save state for unavailable marking
                # This ensures the state file is always updated with the latest account availability
                save_success = self.file_manager.save_state(self.state, force=True)
                if save_success:
                    logger.info(f"Successfully saved state after marking account {account_id} as unavailable")
                    self.pending_changes.clear()
                    self.last_save_time = time.time()
                else:
                    logger.error(f"Failed to save state after marking account {account_id} as unavailable")
                    # Try again with direct write as a fallback
                    try:
                        state_json = json.dumps(self.state, indent=2)
                        direct_success = self.file_manager.direct_write(STATE_FILE_PATH, state_json)
                        if direct_success:
                            logger.info(f"Successfully saved state using direct write after marking account {account_id} as unavailable")
                            self.pending_changes.clear()
                            self.last_save_time = time.time()
                        else:
                            logger.error(f"Failed to save state using direct write after marking account {account_id} as unavailable")
                    except Exception as e:
                        logger.error(f"Error in direct write fallback after marking account unavailable: {e}")

    def is_rate_limited(self, account_id: str) -> bool:
        """
        Check if an account is rate limited using optimized data structures.

        Args:
            account_id: The ID of the account

        Returns:
            bool: True if rate limited, False otherwise
        """
        # Check if in rate-limited cache first (O(1))
        if account_id in self.rate_limited_accounts:
            expiry_time = self.rate_limited_accounts[account_id]
            if time.time() < expiry_time:
                return True
            else:
                # Expired, remove from rate-limited cache
                del self.rate_limited_accounts[account_id]
                # Add back to available accounts if the account is still available
                if account_id in self.accounts_by_id and self.accounts_by_id[account_id].get("availability") == "available":
                    self.available_accounts.add(account_id)
                return False

        # Calculate rate limits on demand
        is_limited, expiry = self._calculate_rate_limits(account_id)
        if is_limited:
            # Add to rate-limited cache with expiry time
            self.rate_limited_accounts[account_id] = expiry
            # Remove from available accounts
            self.available_accounts.discard(account_id)
            return True

        return False

    def check_rate_limits(self, account_id: str) -> bool:
        """
        Legacy method for checking if an account has reached rate limits.
        Uses the new optimized is_rate_limited method.

        Args:
            account_id: The ID of the account

        Returns:
            bool: True if rate limits reached, False otherwise
        """
        return self.is_rate_limited(account_id)

    def select_account_round_robin(self) -> Optional[Dict[str, Any]]:
        """
        Select an account using round-robin strategy with optimized data structures.

        Returns:
            Dict: The selected account, or None if no accounts
        """
        # Clean up expired rate limits first
        self._cleanup_expired_rate_limits()

        # Get available accounts that are not rate limited
        available_not_limited = self.get_available_accounts()
        if not available_not_limited:
            return None

        # Get current index
        current_index = self.state.get("current_account_index", 0)
        accounts = self.state.get("accounts", [])

        # Find the next available account that's not rate limited
        # This is still O(n) but we're working with a filtered list now
        start_index = current_index
        while True:
            current_index = (current_index + 1) % len(accounts)
            account = accounts[current_index]
            account_id = account.get("id")

            # Check if account is available and not rate limited using O(1) lookups
            if (account_id in self.available_accounts and
                account_id not in self.rate_limited_accounts):
                # Update current index
                self.state["current_account_index"] = current_index
                return account

            # If we've checked all accounts, break
            if current_index == start_index:
                break

        # If no suitable account found, return None
        return None

    def select_account_random(self) -> Optional[Dict[str, Any]]:
        """
        Select an account using random strategy with optimized data structures.

        Returns:
            Dict: The selected account, or None if no accounts
        """
        # Get available accounts that are not rate limited
        available_not_limited = self.get_available_accounts()
        if not available_not_limited:
            return None

        # Select a random account
        account = random.choice(available_not_limited)

        # Update current index
        for i, acc in enumerate(self.state.get("accounts", [])):
            if acc.get("id") == account.get("id"):
                self.state["current_account_index"] = i
                break

        return account

    def select_account_least_used(self) -> Optional[Dict[str, Any]]:
        """
        Select an account using least-used strategy with optimized data structures.

        Returns:
            Dict: The selected account, or None if no accounts
        """
        # Get available accounts that are not rate limited
        available_not_limited = self.get_available_accounts()
        if not available_not_limited:
            return None

        # Sort accounts by total requests
        sorted_accounts = sorted(
            available_not_limited,
            key=lambda acc: self.state.get("usage_stats", {}).get(acc.get("id"), {}).get("total_requests", 0)
        )

        # Select the least used account
        account = sorted_accounts[0]

        # Update current index
        for i, acc in enumerate(self.state.get("accounts", [])):
            if acc.get("id") == account.get("id"):
                self.state["current_account_index"] = i
                break

        return account

    async def check_and_rotate_if_needed(self) -> Tuple[bool, bool, Optional[Dict[str, Any]]]:
        """
        Check if the current account is rate limited and rotate if needed.

        Returns:
            Tuple[bool, bool, Optional[Dict[str, Any]]]: (was_rotation_needed, rotation_success, new_account)
        """
        # Get the current account
        current_account = self.get_current_account()
        if not current_account:
            logger.warning("No current account found, rotation needed")
            success, account = await self.rotate_account()
            if not success:
                logger.error("Failed to rotate account and no current account exists")
                # Return the best we can in this situation
                return True, False, None
            return True, success, account

        # Check if the current account is rate limited
        account_id = current_account.get("id")
        if not account_id:
            logger.warning("Current account has no ID, rotation needed")
            success, account = await self.rotate_account()
            if not success:
                logger.error("Failed to rotate account and current account has no ID")
                # Return the current account even though it has no ID
                return True, False, current_account
            return True, success, account

        # Check if rate limited
        if self.is_rate_limited(account_id):
            logger.info(f"Current account {account_id} is rate limited, rotating")
            success, account = await self.rotate_account()
            if not success:
                logger.warning(f"Failed to rotate from rate-limited account {account_id}")
                # Check if we have any available accounts at all
                available_accounts = self.get_available_accounts()
                if not available_accounts:
                    logger.error("No available accounts found, all accounts may be rate limited")
                    # Return the current account even though it's rate limited
                    return True, False, current_account
                else:
                    logger.error(f"Failed to rotate despite having {len(available_accounts)} available accounts")
                    return True, False, current_account
            return True, success, account

        # No rotation needed
        return False, True, current_account

    async def rotate_account(self, strategy: Optional[str] = None) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """
        Rotate to the next account using optimized data structures.

        Args:
            strategy: The rotation strategy to use

        Returns:
            Tuple[bool, Optional[Dict[str, Any]]]: (success, account)
        """
        async with self.auth_file_lock:
            # Clean up expired rate limits first
            self._cleanup_expired_rate_limits()

            # Use specified strategy or default from config
            if not strategy:
                strategy = CONFIG["rotation_strategy"]

            # Select account based on strategy
            if strategy == "round-robin":
                account = self.select_account_round_robin()
            elif strategy == "random":
                account = self.select_account_random()
            elif strategy == "least-used":
                account = self.select_account_least_used()
            else:
                logger.error(f"Unknown rotation strategy: {strategy}")
                return False, None

            if not account:
                logger.error("No available account found for rotation")
                return False, None

            # Update last rotation time
            current_time = time.time()
            self.state["last_rotation_time"] = current_time

            # Add to pending changes for batched state persistence
            self.pending_changes.add("rotation")

            # Force save state for rotations
            # This ensures the state file is always updated with the latest rotation information
            save_success = self.file_manager.save_state(self.state, force=True)
            if save_success:
                logger.info(f"Successfully saved state after rotation to account {account.get('id')}")
                self.pending_changes.clear()
                self.last_save_time = current_time
            else:
                logger.error(f"Failed to save state after rotation to account {account.get('id')}")
                # Try again with direct write as a fallback
                try:
                    state_json = json.dumps(self.state, indent=2)
                    direct_success = self.file_manager.direct_write(STATE_FILE_PATH, state_json)
                    if direct_success:
                        logger.info(f"Successfully saved state using direct write after rotation to account {account.get('id')}")
                        self.pending_changes.clear()
                        self.last_save_time = current_time
                    else:
                        logger.error(f"Failed to save state using direct write after rotation to account {account.get('id')}")
                except Exception as e:
                    logger.error(f"Error in direct write fallback after rotation: {e}")

            # Update auth file
            success = self.file_manager.update_auth_file(account)
            if not success:
                logger.error(f"Failed to update auth file with account {account.get('id')}")
                return False, account

            logger.info(f"Rotated to account {account.get('id')}")
            return True, account

    async def check_failed_accounts(self):
        """Check if failed accounts have recovered using optimized data structures."""
        async with self.lock:
            # Get all unavailable accounts from the state
            unavailable_accounts = []
            for account in self.state.get("accounts", []):
                if account.get("availability") == "unavailable":
                    account_id = account.get("id")
                    if account_id:
                        unavailable_accounts.append((account_id, account))

            # Track if any accounts were recovered
            recovered = False

            # Check each unavailable account
            for account_id, account in unavailable_accounts:
                # Check if account has been in timeout long enough
                last_failure_time = self.state.get("health_stats", {}).get(account_id, {}).get("last_failure_time")
                if last_failure_time and time.time() - last_failure_time > CONFIG["connection_failure_timeout"]:
                    logger.info(f"Account {account_id} has been in timeout long enough, marking as available")

                    # Update in state
                    account["availability"] = "available"

                    # Update in optimized data structures
                    if account_id in self.accounts_by_id:
                        self.accounts_by_id[account_id]["availability"] = "available"
                        self.available_accounts.add(account_id)

                    # Reset failure counters
                    if account_id in self.connection_failures:
                        self.connection_failures[account_id] = 0
                    if "health_stats" in self.state and account_id in self.state["health_stats"]:
                        self.state["health_stats"][account_id]["connection_failures"] = 0

                    # Add to pending changes
                    self.pending_changes.add(account_id)
                    recovered = True

            # Save state if any accounts were recovered
            if recovered:
                # Force save state for account recovery
                save_success = self.file_manager.save_state(self.state, force=True)
                if save_success:
                    logger.info(f"Successfully saved state after recovering {len(unavailable_accounts)} accounts")
                    self.pending_changes.clear()
                    self.last_save_time = time.time()
                else:
                    logger.error(f"Failed to save state after recovering accounts")
                    # Try again with direct write as a fallback
                    try:
                        state_json = json.dumps(self.state, indent=2)
                        direct_success = self.file_manager.direct_write(STATE_FILE_PATH, state_json)
                        if direct_success:
                            logger.info(f"Successfully saved state using direct write after recovering accounts")
                            self.pending_changes.clear()
                            self.last_save_time = time.time()
                        else:
                            logger.error(f"Failed to save state using direct write after recovering accounts")
                    except Exception as e:
                        logger.error(f"Error in direct write fallback after recovering accounts: {e}")
