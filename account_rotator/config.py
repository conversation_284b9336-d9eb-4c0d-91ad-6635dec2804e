#!/usr/bin/env python3
"""
Configuration settings for the account rotation system.
"""
import os
import json
from typing import Dict, Any

# File paths
HAR_COOKIES_DIR = "/app/har_and_cookies"
AUTH_FILES_DIR = "/app/authentication_files"
AUTH_FILE_PATH = f"{HAR_COOKIES_DIR}/auth_Chatshare.json"
STATE_FILE_PATH = f"{AUTH_FILES_DIR}/account_rotator_state.json"
CONFIG_FILE_PATH = "/app/account_rotator_config.json"

# Remote accounts URL
DEFAULT_REMOTE_ACCOUNTS_URL = "https://gist.github.com/ThinkFar/4b61378f3e46ca8d5a86e5da669ffe51/raw/auth_g4fcs.json"
REMOTE_ACCOUNTS_URL = os.environ.get("REMOTE_ACCOUNTS_URL", DEFAULT_REMOTE_ACCOUNTS_URL)

# Default configuration
DEFAULT_CONFIG = {
    "rate_limits": {
        "requests_per_minute_limit": 15,
        "min_delay_seconds": 4,
        "daily_request_limit": 15267
    },
    "rotation_interval": 86400,  # 24 hours - effectively disabled time-based rotation
    "rotation_strategy": "round-robin",  # round-robin, random, least-used
    "remote_check_interval": 10,  # 10 seconds
    "max_connection_failures": 3,
    "connection_failure_timeout": 300,  # 5 minutes
    "batch_threshold": 5,  # Number of changes before triggering a state save
    "state_save_interval": 300,  # 5 minutes between forced state saves
    "check_all_limits_always": False,  # If True, always check all rate limits; if False, return early when any limit is hit
    "cleanup_interval": 300,  # 5 minutes between cleanup of expired rate limits
    "inactive_account_threshold": 3600,  # 1 hour of inactivity before cleaning up request tracking
    "max_request_times": 1000,  # Maximum number of accounts to track request times for
    "max_rate_limited_accounts": 1000,  # Maximum number of rate limited accounts to track
    "max_pending_changes": 1000  # Maximum number of pending changes before forcing a state save
}

def load_config() -> Dict[str, Any]:
    """
    Load configuration from file or use defaults.

    Returns:
        Dict[str, Any]: Configuration dictionary
    """
    # Start with default config
    config = DEFAULT_CONFIG.copy()

    # Try to load config from file
    if os.path.exists(CONFIG_FILE_PATH):
        try:
            with open(CONFIG_FILE_PATH, 'r') as f:
                file_config = json.load(f)
                # Update config with values from file
                for key, value in file_config.items():
                    if key in config:
                        if isinstance(value, dict) and isinstance(config[key], dict):
                            # Merge nested dictionaries
                            config[key].update(value)
                        else:
                            # Replace simple values
                            config[key] = value
                    else:
                        # Add new keys
                        config[key] = value
        except Exception as e:
            print(f"Error loading config from {CONFIG_FILE_PATH}: {e}")

    # Override with environment variables if present
    if os.environ.get("ROTATION_INTERVAL"):
        try:
            config["rotation_interval"] = int(os.environ.get("ROTATION_INTERVAL"))
        except ValueError:
            pass

    if os.environ.get("ROTATION_STRATEGY"):
        strategy = os.environ.get("ROTATION_STRATEGY")
        if strategy in ["round-robin", "random", "least-used"]:
            config["rotation_strategy"] = strategy

    if os.environ.get("REMOTE_CHECK_INTERVAL"):
        try:
            config["remote_check_interval"] = int(os.environ.get("REMOTE_CHECK_INTERVAL"))
        except ValueError:
            pass

    # New environment variable for rate limit-based rotation
    if os.environ.get("RATE_LIMIT_ROTATION_ENABLED"):
        value = os.environ.get("RATE_LIMIT_ROTATION_ENABLED").lower()
        if value in ["true", "1", "yes", "y"]:
            # If rate limit rotation is enabled, set rotation_interval to a high value
            # to effectively disable time-based rotation
            config["rotation_interval"] = 86400  # 24 hours
            print("Rate limit-based rotation enabled, time-based rotation disabled")
        elif value in ["false", "0", "no", "n"]:
            # If rate limit rotation is disabled, keep the rotation_interval as is
            # for time-based rotation
            print("Rate limit-based rotation disabled, using time-based rotation")

    # Environment variables for collection size limits
    if os.environ.get("MAX_REQUEST_TIMES"):
        try:
            config["max_request_times"] = int(os.environ.get("MAX_REQUEST_TIMES"))
        except ValueError:
            pass

    if os.environ.get("MAX_RATE_LIMITED_ACCOUNTS"):
        try:
            config["max_rate_limited_accounts"] = int(os.environ.get("MAX_RATE_LIMITED_ACCOUNTS"))
        except ValueError:
            pass

    if os.environ.get("MAX_PENDING_CHANGES"):
        try:
            config["max_pending_changes"] = int(os.environ.get("MAX_PENDING_CHANGES"))
        except ValueError:
            pass

    # Validate the configuration
    return validate_config(config)

def validate_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate configuration values and set defaults for missing values.

    Args:
        config: The configuration to validate

    Returns:
        Dict: The validated configuration
    """
    # Create a copy to avoid modifying the original
    validated = config.copy()

    # Ensure rate_limits exists and has valid values
    if "rate_limits" not in validated:
        validated["rate_limits"] = DEFAULT_CONFIG["rate_limits"].copy()
    else:
        # Ensure all rate limit keys exist
        for key, default_value in DEFAULT_CONFIG["rate_limits"].items():
            if key not in validated["rate_limits"]:
                validated["rate_limits"][key] = default_value
            elif not isinstance(validated["rate_limits"][key], (int, float)) or validated["rate_limits"][key] <= 0:
                print(f"Invalid rate limit {key}: {validated['rate_limits'][key]}, using default {default_value}")
                validated["rate_limits"][key] = default_value

    # Validate rotation_interval
    if "rotation_interval" not in validated or not isinstance(validated["rotation_interval"], (int, float)) or validated["rotation_interval"] < 0:
        validated["rotation_interval"] = DEFAULT_CONFIG["rotation_interval"]

    # Validate rotation_strategy
    valid_strategies = ["round-robin", "random", "least-used"]
    if "rotation_strategy" not in validated or validated["rotation_strategy"] not in valid_strategies:
        validated["rotation_strategy"] = DEFAULT_CONFIG["rotation_strategy"]

    # Validate remote_check_interval
    if "remote_check_interval" not in validated or not isinstance(validated["remote_check_interval"], (int, float)) or validated["remote_check_interval"] < 1:
        validated["remote_check_interval"] = DEFAULT_CONFIG["remote_check_interval"]

    # Validate max_connection_failures
    if "max_connection_failures" not in validated or not isinstance(validated["max_connection_failures"], int) or validated["max_connection_failures"] < 1:
        validated["max_connection_failures"] = DEFAULT_CONFIG["max_connection_failures"]

    # Validate connection_failure_timeout
    if "connection_failure_timeout" not in validated or not isinstance(validated["connection_failure_timeout"], (int, float)) or validated["connection_failure_timeout"] < 0:
        validated["connection_failure_timeout"] = DEFAULT_CONFIG["connection_failure_timeout"]

    # Validate batch_threshold
    if "batch_threshold" not in validated or not isinstance(validated["batch_threshold"], int) or validated["batch_threshold"] < 1:
        validated["batch_threshold"] = DEFAULT_CONFIG["batch_threshold"]

    # Validate state_save_interval
    if "state_save_interval" not in validated or not isinstance(validated["state_save_interval"], (int, float)) or validated["state_save_interval"] < 0:
        validated["state_save_interval"] = DEFAULT_CONFIG["state_save_interval"]

    # Validate check_all_limits_always
    if "check_all_limits_always" not in validated or not isinstance(validated["check_all_limits_always"], bool):
        validated["check_all_limits_always"] = DEFAULT_CONFIG["check_all_limits_always"]

    # Validate cleanup_interval
    if "cleanup_interval" not in validated or not isinstance(validated["cleanup_interval"], (int, float)) or validated["cleanup_interval"] < 0:
        validated["cleanup_interval"] = DEFAULT_CONFIG["cleanup_interval"]

    # Validate inactive_account_threshold
    if "inactive_account_threshold" not in validated or not isinstance(validated["inactive_account_threshold"], (int, float)) or validated["inactive_account_threshold"] < 0:
        validated["inactive_account_threshold"] = DEFAULT_CONFIG["inactive_account_threshold"]

    # Validate max_request_times
    if "max_request_times" not in validated or not isinstance(validated["max_request_times"], int) or validated["max_request_times"] < 1:
        validated["max_request_times"] = DEFAULT_CONFIG["max_request_times"]

    # Validate max_rate_limited_accounts
    if "max_rate_limited_accounts" not in validated or not isinstance(validated["max_rate_limited_accounts"], int) or validated["max_rate_limited_accounts"] < 1:
        validated["max_rate_limited_accounts"] = DEFAULT_CONFIG["max_rate_limited_accounts"]

    # Validate max_pending_changes
    if "max_pending_changes" not in validated or not isinstance(validated["max_pending_changes"], int) or validated["max_pending_changes"] < 1:
        validated["max_pending_changes"] = DEFAULT_CONFIG["max_pending_changes"]

    return validated

# Sample accounts for fallback
SAMPLE_ACCOUNTS = [
    {
        "id": "sample_account_1",
        "availability": "available",
        "har": {
            "cookies": {
                "gfsessionid": "sample-session-id-1",
                "chatshare-message": "true",
                "authorization": "sample-auth-token-1",
                "token": "sample-token-1"
            },
            "headers": {
                "accept": "*/*",
                "accept-encoding": "gzip, deflate, br",
                "accept-language": "en-US",
                "referer": "https://chatshare.biz",
                "sec-ch-ua": "\"Not(A:Brand\";v=\"99\",\"Google Chrome\";v=\"133\",\"Chromium\";v=\"133\"",
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": "\"Windows\"",
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-origin",
                "user-agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
            }
        }
    },
    {
        "id": "sample_account_2",
        "availability": "available",
        "har": {
            "cookies": {
                "gfsessionid": "sample-session-id-2",
                "chatshare-message": "true",
                "authorization": "sample-auth-token-2",
                "token": "sample-token-2"
            },
            "headers": {
                "accept": "*/*",
                "accept-encoding": "gzip, deflate, br",
                "accept-language": "en-US",
                "referer": "https://chatshare.biz",
                "sec-ch-ua": "\"Not(A:Brand\";v=\"99\",\"Google Chrome\";v=\"133\",\"Chromium\";v=\"133\"",
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": "\"Windows\"",
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-origin",
                "user-agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
            }
        }
    }
]

# Load configuration
CONFIG = load_config()
