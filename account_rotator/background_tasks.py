#!/usr/bin/env python3
"""
Background tasks for the account rotation system.
Handles periodic tasks like account rotation and remote account fetching.
"""
import asyncio
import logging
import time

from .config import CONFIG
from .account_manager import AccountManager

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BackgroundTaskManager:
    """Manages background tasks for the account rotation system."""

    def __init__(self, account_manager: AccountManager):
        """
        Initialize the background task manager.

        Args:
            account_manager: The account manager instance
        """
        self.account_manager = account_manager
        self.is_running = False
        self.tasks = []

    async def start(self):
        """Start background tasks."""
        if self.is_running:
            logger.warning("Background tasks already running")
            return

        self.is_running = True
        logger.info("Starting background tasks")

        # Create tasks - no longer using time-based rotation
        self.tasks = [
            asyncio.create_task(self._run_remote_check_task()),
            asyncio.create_task(self._run_failed_accounts_check_task()),
            asyncio.create_task(self._run_cleanup_task())
        ]

    async def stop(self):
        """Stop background tasks."""
        if not self.is_running:
            logger.warning("Background tasks not running")
            return

        logger.info("Stopping background tasks")
        self.is_running = False

        # Cancel tasks
        for task in self.tasks:
            task.cancel()

        # Wait for tasks to complete
        if self.tasks:
            await asyncio.gather(*self.tasks, return_exceptions=True)

        self.tasks = []
        logger.info("Background tasks stopped")

    async def _run_remote_check_task(self):
        """Run the remote check task with robust error handling and retry mechanism."""
        logger.info("Starting remote check task")

        # Check if aiohttp is available
        try:
            # Just try to import it to see if it's available
            import aiohttp  # noqa
        except ImportError:
            logger.error("aiohttp is not installed. Remote account fetching is disabled.")
            logger.error("Please install it using: apt-get install python3-aiohttp")
            return

        # Initialize retry tracking
        retry_count = 0
        max_retries = 5
        last_success_time = time.time()
        health_check_interval = 300  # 5 minutes

        try:
            while self.is_running:
                try:
                    # Fetch remote accounts
                    success, message = await self.account_manager.fetch_remote_accounts()
                    if success:
                        logger.debug(f"Remote check task: {message}")
                        # Reset retry count on success
                        retry_count = 0
                        last_success_time = time.time()
                    else:
                        logger.error(f"Remote check task failed: {message}")
                        retry_count += 1
                except Exception as e:
                    logger.error(f"Error in remote check task: {e}")
                    retry_count += 1

                # Implement exponential backoff for retries
                if retry_count > 0:
                    # Calculate backoff time (with maximum cap)
                    backoff_time = min(CONFIG["remote_check_interval"] * (2 ** retry_count), 3600)  # Max 1 hour
                    logger.warning(f"Remote check task will retry in {backoff_time} seconds (retry {retry_count}/{max_retries})")
                    await asyncio.sleep(backoff_time)
                else:
                    # Normal interval if no errors
                    await asyncio.sleep(CONFIG["remote_check_interval"])

                # Health check - if too many retries or too long since last success, reset
                if (retry_count >= max_retries or
                    (time.time() - last_success_time > health_check_interval and retry_count > 0)):
                    logger.warning("Remote check task performing health reset due to persistent failures")
                    # Reset retry count but keep track of the reset
                    retry_count = 1  # Start with 1 to indicate we've had a reset
                    # Try to recover by reinitializing any necessary components
                    try:
                        # Reinitialize account manager's optimized structures
                        self.account_manager._initialize_optimized_structures()
                        logger.info("Remote check task successfully reinitialized data structures")
                    except Exception as reset_error:
                        logger.error(f"Error during remote check task reset: {reset_error}")
        except asyncio.CancelledError:
            logger.info("Remote check task cancelled")
        except Exception as e:
            logger.error(f"Remote check task failed with unhandled exception: {e}")
            # Try to restart the task after a delay
            if self.is_running:
                logger.info("Attempting to restart remote check task in 60 seconds")
                await asyncio.sleep(60)
                asyncio.create_task(self._run_remote_check_task())

    async def _run_cleanup_task(self):
        """Run the cleanup task for optimized data structures with robust error handling."""
        logger.info("Starting cleanup task")

        # Initialize retry tracking
        retry_count = 0
        max_retries = 5
        last_success_time = time.time()
        health_check_interval = 300  # 5 minutes

        try:
            while self.is_running:
                try:
                    # Run all cleanup operations
                    self.account_manager.cleanup()

                    # Check if the current account is rate limited and needs rotation
                    was_needed, success, account = await self.account_manager.check_and_rotate_if_needed()
                    if was_needed:
                        if success and account:
                            logger.info(f"Cleanup task: Rotated to account {account.get('id')} due to rate limits")
                        else:
                            logger.error("Cleanup task: Rate limit rotation failed")

                    # Task completed successfully
                    retry_count = 0
                    last_success_time = time.time()
                except Exception as e:
                    logger.error(f"Error in cleanup task: {e}")
                    retry_count += 1

                # Implement exponential backoff for retries
                if retry_count > 0:
                    # Calculate backoff time (with maximum cap)
                    backoff_time = min(5 * (2 ** retry_count), 300)  # Max 5 minutes
                    logger.warning(f"Cleanup task will retry in {backoff_time} seconds (retry {retry_count}/{max_retries})")
                    await asyncio.sleep(backoff_time)
                else:
                    # Normal interval if no errors
                    await asyncio.sleep(10)  # Every 10 seconds

                # Health check - if too many retries or too long since last success, reset
                if (retry_count >= max_retries or
                    (time.time() - last_success_time > health_check_interval and retry_count > 0)):
                    logger.warning("Cleanup task performing health reset due to persistent failures")
                    # Reset retry count but keep track of the reset
                    retry_count = 1  # Start with 1 to indicate we've had a reset
                    # Try to recover by reinitializing any necessary components
                    try:
                        # Reinitialize account manager's optimized structures
                        self.account_manager._initialize_optimized_structures()
                        logger.info("Cleanup task successfully reinitialized data structures")
                    except Exception as reset_error:
                        logger.error(f"Error during cleanup task reset: {reset_error}")
        except asyncio.CancelledError:
            logger.info("Cleanup task cancelled")
        except Exception as e:
            logger.error(f"Cleanup task failed with unhandled exception: {e}")
            # Try to restart the task after a delay
            if self.is_running:
                logger.info("Attempting to restart cleanup task in 30 seconds")
                await asyncio.sleep(30)
                asyncio.create_task(self._run_cleanup_task())

    async def _run_failed_accounts_check_task(self):
        """Run the failed accounts check task with robust error handling."""
        logger.info("Starting failed accounts check task")

        # Initialize retry tracking
        retry_count = 0
        max_retries = 5
        last_success_time = time.time()
        health_check_interval = 300  # 5 minutes

        try:
            while self.is_running:
                try:
                    # Check failed accounts
                    await self.account_manager.check_failed_accounts()

                    # Task completed successfully
                    retry_count = 0
                    last_success_time = time.time()
                except Exception as e:
                    logger.error(f"Error in failed accounts check task: {e}")
                    retry_count += 1

                # Implement exponential backoff for retries
                if retry_count > 0:
                    # Calculate backoff time (with maximum cap)
                    backoff_time = min(30 * (2 ** retry_count), 600)  # Max 10 minutes
                    logger.warning(f"Failed accounts check task will retry in {backoff_time} seconds (retry {retry_count}/{max_retries})")
                    await asyncio.sleep(backoff_time)
                else:
                    # Normal interval if no errors
                    await asyncio.sleep(60)  # Every minute

                # Health check - if too many retries or too long since last success, reset
                if (retry_count >= max_retries or
                    (time.time() - last_success_time > health_check_interval and retry_count > 0)):
                    logger.warning("Failed accounts check task performing health reset due to persistent failures")
                    # Reset retry count but keep track of the reset
                    retry_count = 1  # Start with 1 to indicate we've had a reset
                    # Try to recover by reinitializing any necessary components
                    try:
                        # Reinitialize account manager's optimized structures
                        self.account_manager._initialize_optimized_structures()
                        logger.info("Failed accounts check task successfully reinitialized data structures")
                    except Exception as reset_error:
                        logger.error(f"Error during failed accounts check task reset: {reset_error}")
        except asyncio.CancelledError:
            logger.info("Failed accounts check task cancelled")
        except Exception as e:
            logger.error(f"Failed accounts check task failed with unhandled exception: {e}")
            # Try to restart the task after a delay
            if self.is_running:
                logger.info("Attempting to restart failed accounts check task in 30 seconds")
                await asyncio.sleep(30)
                asyncio.create_task(self._run_failed_accounts_check_task())
