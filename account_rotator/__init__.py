#!/usr/bin/env python3
# Make sure this file is executed when the module is imported
print("Initializing account_rotator module...")
"""
Account rotation system for the Chatshare provider in G4F.
Dynamically switches the active Chatshare account by replacing the auth file content.
"""
import os
import asyncio
import logging
import signal
from typing import Dict, Any, Optional

# Check if aiohttp is installed
try:
    # Just try to import it to see if it's available
    import aiohttp  # noqa
    print("aiohttp is already installed in account_rotator module")
except ImportError:
    print("aiohttp is not installed in account_rotator module.")
    print("Please install it using: apt-get install python3-aiohttp")
    print("Continuing anyway, but this will cause problems with remote account fetching.")

from .account_manager import AccountManager
from .background_tasks import BackgroundTaskManager

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import config to access file paths
from .config import AUTH_FILE_PATH

# Ensure auth file exists
def ensure_auth_file_exists():
    """Ensure auth file exists with at least empty content."""
    import json
    if not os.path.exists(AUTH_FILE_PATH) or os.path.getsize(AUTH_FILE_PATH) == 0:
        logger.info(f"Auth file does not exist or is empty, creating default auth file: {AUTH_FILE_PATH}")
        try:
            # Create a default auth file with empty cookies and headers
            default_content = {
                "cookies": {},
                "headers": {}
            }
            content_json = json.dumps(default_content, indent=2)

            # Ensure directory exists
            os.makedirs(os.path.dirname(AUTH_FILE_PATH), exist_ok=True)

            # Try direct write
            with open(AUTH_FILE_PATH, 'w') as f:
                f.write(content_json)
                f.flush()
                os.fsync(f.fileno())

            # Set permissions
            try:
                # Set very permissive permissions (rw-rw-rw-)
                os.chmod(AUTH_FILE_PATH, 0o666)
                logger.info(f"Set permissions on default auth file to 0o666")

                # Also set permissions on the directory to ensure it's accessible
                dir_path = os.path.dirname(AUTH_FILE_PATH)
                os.chmod(dir_path, 0o777)  # rwxrwxrwx
                logger.info(f"Set permissions on directory {dir_path} to 0o777")

                # Log file stats for debugging
                import stat
                st = os.stat(AUTH_FILE_PATH)
                logger.info(f"Auth file permissions: {stat.filemode(st.st_mode)}")
                logger.info(f"Auth file owner: {st.st_uid}, group: {st.st_gid}")

                # Log directory stats for debugging
                st_dir = os.stat(dir_path)
                logger.info(f"Directory {dir_path} permissions: {stat.filemode(st_dir.st_mode)}")
                logger.info(f"Directory {dir_path} owner: {st_dir.st_uid}, group: {st_dir.st_gid}")
            except Exception as e:
                logger.warning(f"Could not set permissions on default auth file: {e}")

            logger.info(f"Created default auth file: {AUTH_FILE_PATH}")
        except Exception as e:
            logger.error(f"Error creating default auth file: {e}")
    else:
        logger.debug(f"Auth file already exists: {AUTH_FILE_PATH}")

# Call the function to ensure auth file exists
ensure_auth_file_exists()

class AccountRotator:
    """Main class for the account rotation system."""

    _instance = None

    @classmethod
    def get_instance(cls):
        """Get singleton instance."""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    def __init__(self):
        """Initialize the account rotator."""
        if AccountRotator._instance is not None:
            raise RuntimeError("AccountRotator is a singleton, use get_instance() instead")

        # Initialize account manager
        self.account_manager = AccountManager()

        # Initialize background task manager
        self.background_tasks = BackgroundTaskManager(self.account_manager)

        # Initialize event loop
        self.loop = None

    async def start(self):
        """Start the account rotator."""
        logger.info("Starting account rotator")

        # Apply the current account to the auth file
        current_account = self.account_manager.get_current_account()
        if current_account:
            logger.info(f"Applying current account {current_account.get('id')} to auth file during startup")
            success = self.account_manager.file_manager.update_auth_file(current_account)
            if success:
                logger.info(f"Successfully applied account {current_account.get('id')} to auth file")
            else:
                logger.error(f"Failed to apply account {current_account.get('id')} to auth file")
        else:
            # If no current account, try to rotate to an available account
            logger.info("No current account found, trying to rotate to an available account")
            success, account = await self.account_manager.rotate_account()
            if success:
                logger.info(f"Successfully rotated to account {account.get('id')} during startup")
            else:
                logger.error("Failed to rotate to an account during startup")

        # Start background tasks
        await self.background_tasks.start()

    async def stop(self):
        """Stop the account rotator."""
        logger.info("Stopping account rotator")

        # Stop background tasks
        await self.background_tasks.stop()

    def get_current_account(self) -> Optional[Dict[str, Any]]:
        """
        Get the currently active account.

        Returns:
            Dict: The current account, or None if no accounts
        """
        return self.account_manager.get_current_account()

    async def rotate_account(self, strategy: Optional[str] = None) -> bool:
        """
        Manually rotate to the next account.

        Args:
            strategy: The rotation strategy to use

        Returns:
            bool: True if rotation was successful, False otherwise
        """
        success, _ = await self.account_manager.rotate_account(strategy)
        return success

    async def check_and_rotate_if_needed(self) -> bool:
        """
        Check if the current account is rate limited and rotate if needed.

        Returns:
            bool: True if rotation was successful or not needed, False if rotation failed
        """
        was_needed, success, _ = await self.account_manager.check_and_rotate_if_needed()
        if was_needed:
            return success
        return True

    def is_rate_limited(self, account_id: Optional[str] = None) -> bool:
        """
        Check if an account is rate limited.

        Args:
            account_id: The ID of the account, or None to use current account

        Returns:
            bool: True if rate limited, False otherwise
        """
        if account_id is None:
            current_account = self.account_manager.get_current_account()
            if current_account:
                account_id = current_account.get("id")
            else:
                return False

        if account_id:
            return self.account_manager.is_rate_limited(account_id)
        return False

    async def record_request(self, account_id: Optional[str] = None):
        """
        Record a request for rate limiting.

        Args:
            account_id: The ID of the account, or None to use current account
        """
        if account_id is None:
            current_account = self.account_manager.get_current_account()
            if current_account:
                account_id = current_account.get("id")

        if account_id:
            await self.account_manager.record_request(account_id)

    async def record_failure(self, account_id: Optional[str] = None):
        """
        Record a connection failure.

        Args:
            account_id: The ID of the account, or None to use current account
        """
        if account_id is None:
            current_account = self.account_manager.get_current_account()
            if current_account:
                account_id = current_account.get("id")

        if account_id:
            await self.account_manager.record_failure(account_id)

    async def record_success(self, account_id: Optional[str] = None):
        """
        Record a successful connection.

        Args:
            account_id: The ID of the account, or None to use current account
        """
        if account_id is None:
            current_account = self.account_manager.get_current_account()
            if current_account:
                account_id = current_account.get("id")

        if account_id:
            await self.account_manager.record_success(account_id)

    def cleanup(self):
        """
        Run all cleanup operations.

        This includes:
        - Cleaning up expired rate limits
        - Cleaning up inactive accounts
        - Cleaning up pending changes if too many
        """
        self.account_manager.cleanup()

# Global instance
account_rotator = AccountRotator.get_instance()

async def main():
    """Main entry point for the account rotator."""
    # Setup signal handlers
    # Get the current event loop
    loop = asyncio.get_running_loop()

    # Define shutdown coroutine
    async def shutdown():
        logger.info("Received shutdown signal")
        await account_rotator.stop()
        # No need to stop the loop as asyncio.run will handle that

    # Register signal handlers
    for sig in (signal.SIGINT, signal.SIGTERM):
        loop.add_signal_handler(sig, lambda: asyncio.create_task(shutdown()))

    try:
        # Start account rotator
        await account_rotator.start()

        # Keep running until stopped
        while True:
            await asyncio.sleep(1)
    except asyncio.CancelledError:
        pass
    finally:
        await account_rotator.stop()

if __name__ == "__main__":
    # Run main function using modern asyncio approach
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("Received KeyboardInterrupt, shutting down...")
    except Exception as e:
        print(f"Error in main function: {e}")
        import traceback
        traceback.print_exc()
