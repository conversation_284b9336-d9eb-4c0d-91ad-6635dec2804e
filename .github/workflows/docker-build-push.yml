# Workflow for building and pushing the Docker image
name: Build and Push Docker Image

# Workflow is currently disabled
# Trigger the workflow on pushes to the main branch (currently disabled)
on:
  # Disable the workflow by using a branch that doesn't exist
  push:
    branches:
      - disabled-workflow-branch # This branch doesn't exist, so the workflow won't run

# Define permissions for the job steps
permissions:
  contents: read    # Required for actions/checkout to access repository code
  packages: write   # Required for docker/login-action and docker/build-push-action to push images

jobs:
  build-and-push:
    runs-on: ubuntu-latest   # Use the latest GitHub-hosted Ubuntu runner

    steps:
      # Step 1: Check out the repository's code
      - name: Checkout repository
        uses: actions/checkout@v4

      # Step 2: Set up QEMU for emulation so that an amd64-only base image can run on other platforms
      - name: Set up QEMU emulation
        uses: docker/setup-qemu-action@v3

      # Step 3: Set up Docker Buildx to enable multi-platform builds
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          platforms: linux/amd64,linux/arm64

      # Step 4: Log in to GitHub Container Registry (ghcr.io) using the current actor's credentials
      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      # Step 5: Extract Docker image metadata such as tags and labels for the build
      - name: Extract metadata (tags, labels) for First Docker image
        id: meta-first
        uses: docker/metadata-action@v5
        with:
          images: ghcr.io/${{ github.repository }}/g4fcs

      # Step 6: Build and push the Docker image with multi-platform support (amd64 and arm64)
      - name: Build and push First Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          push: true
          platforms: linux/amd64,linux/arm64
          tags: ${{ steps.meta-first.outputs.tags }}
          labels: ${{ steps.meta-first.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
